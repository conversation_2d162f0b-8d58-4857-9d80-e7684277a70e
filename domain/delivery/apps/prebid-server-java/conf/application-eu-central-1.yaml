status-response: "ok"
host-id: ${INSTANCE_ID}
# This probably needs to be changed when the PBSA starts proxying the requests
external-url: https://pbs.adscale.de

vertx:
  worker-pool-size: 20
  uploads-dir: file-uploads
  init-timeout-ms: 5000
  enable-per-client-endpoint-metrics: false

server:
  ssl: false
  http:
    enabled: true
    port: 23765
    server-instances: 1

metrics:
  prometheus:
    enabled: true
    port: 16462
    namespace: prebid_server
    custom-labels-enabled: true

admin:
  port: 16614

cache:
  scheme: http
  host: prebid-cache
  path: /cache
  query: uuid=
  banner-ttl-seconds: 3000
  video-ttl-seconds: 3000

adapters:
  stroeercore:
    enabled: true
    endpoint: http://s2shba/s2sdsh
    usersync:
      enabled: true
      cookie-family-name: stroeerCore
      iframe:
        url: https://js.adscale.de/pbsync.html?gdpr={{gdpr}}&gdpr_consent={{gdpr_consent}}&redirect={{redirect_url}}
        support-cors: false
      redirect:
        url: https://ih.adscale.de/uu?gdpr={{gdpr}}&gdpr_consent={{gdpr_consent}}&cburl={{redirect_url}}
        support-cors: false
  mockbidder:
    enabled: true
    endpoint: http://mocks.external.svc.cluster.local/rtb
    usersync:
      enabled: true
      cookie-family-name: mockbidder
      redirect:
        url: https://ih.adscale.de/uu?gdpr={{gdpr}}&gdpr_consent={{gdpr_consent}}&cburl={{redirect_url}}
        support-cors: false


settings:
  enforce-valid-account: false
  generate-storedrequest-bidrequest-id: true
  filesystem:
    settings-filename: props/account-settings-eu-central-1.yaml
    stored-requests-dir: stored/requests/eu-central-1
    stored-imps-dir: stored/impressions/eu-central-1
    stored-responses-dir: stored/responses/eu-central-1
    categories-dir:

gdpr:
  default-value: 0
  host-vendor-id: 136
  vendorlist:
    default-timeout-ms: 2000
    v2:
      cache-dir: /var/tmp/vendor2
    v3:
      cache-dir: /var/tmp/vendor3

host-cookie:
  domain: adscale.de
