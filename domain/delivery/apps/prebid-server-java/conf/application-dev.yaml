status-response: "ok"
host-id: ${INSTANCE_ID}
external-url: http://prebid-server-java.lsd.test

vertx:
  worker-pool-size: 20
  uploads-dir: file-uploads
  init-timeout-ms: 5000
  enable-per-client-endpoint-metrics: false

server:
  ssl: false
  http:
    enabled: true
    port: 23765
    server-instances: 1

metrics:
  prometheus:
    enabled: true
    port: 16462
    namespace: prebid_server
    custom-labels-enabled: true

admin:
  port: 16614

cache:
  scheme: http
  host: prebid-cache-java.lsd.test
  path: /cache
  query: uuid=
  banner-ttl-seconds: 3000
  video-ttl-seconds: 3000

adapters:
  stroeercore:
    enabled: true
    endpoint: http://prebid-server-adapter.lsd.test/s2sdsh
    usersync:
      cookie-family-name: stroeerCore
      iframe:
        url: https://js.lsd.test/pbsync.html?gdpr={{gdpr}}&gdpr_consent={{gdpr_consent}}&redirect={{redirect_url}}
        support-cors: false
      redirect:
        url: https://user-matcher.lsd.test/uu?gdpr={{gdpr}}&gdpr_consent={{gdpr_consent}}&cburl={{redirect_url}}
        support-cors: false
  mockbidder:
      enabled: true
      endpoint: http://mocks2.service.ssp.consul:3001/rtb
      usersync:
        cookie-family-name: mockbidder
        iframe:
          url: https://js.lsd.test/pbsync.html?gdpr={{gdpr}}&gdpr_consent={{gdpr_consent}}&redirect={{redirect_url}}
          support-cors: false
        redirect:
          url: https://user-matcher.lsd.test/uu?gdpr={{gdpr}}&gdpr_consent={{gdpr_consent}}&cburl={{redirect_url}}
          support-cors: false

settings:
  enforce-valid-account: false
  generate-storedrequest-bidrequest-id: true
  filesystem:
    settings-filename: props/account-settings-dev.yaml
    stored-requests-dir: stored/requests/dev
    stored-imps-dir: stored/impressions/dev
    stored-responses-dir: stored/responses/dev
    categories-dir:

gdpr:
  default-value: 0
  host-vendor-id: 136
  vendorlist:
    default-timeout-ms: 2000
    v2:
      cache-dir: /var/tmp/vendor2
    v3:
      cache-dir: /var/tmp/vendor3

host-cookie:
  domain: lsd.test