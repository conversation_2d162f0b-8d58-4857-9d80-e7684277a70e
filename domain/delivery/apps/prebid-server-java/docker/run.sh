#!/usr/bin/env bash

JAVA_OPTS='-server'
JAVA_OPTS+=" -XX:InitialRAMPercentage=${JVM_INITIAL_RAM_PERCENTAGE:-80.0}"
JAVA_OPTS+=" -XX:MaxRAMPercentage=${JVM_MAX_RAM_PERCENTAGE:-80.0}"
JAVA_OPTS+=" -XX:MinRAMPercentage=${JVM_MIN_RAM_PERCENTAGE:-80.0}"
JAVA_OPTS+=' -Dfile.encoding=UTF8'
JAVA_OPTS+=' -XX:+UseG1GC'
JAVA_OPTS+=' -XX:+UseStringDeduplication'
JAVA_OPTS+=' -Djava.awt.headless=true'

if [[ "${DEBUG}" == 'true' ]]; then
    echo 'Debugging container...'
    JAVA_OPTS+=" -Xrunjdwp:server=y,transport=dt_socket,address=*:19686,suspend=n "

    # kotlin coroutine opts
    JAVA_OPTS+=" -Dkotlinx.coroutines.debug "
fi

echo "\$JAVA_OPTS set to: \"${JAVA_OPTS}\""

env | sort

mkdir -p stored/impressions/${CONFIG_ENVIRONMENT_NAME}
mkdir -p stored/responses/${CONFIG_ENVIRONMENT_NAME}
mkdir -p stored/requests/${CONFIG_ENVIRONMENT_NAME}

APPLICATION_CONFIG_FILE="${APP_PROPS_ROOT}/application-${CONFIG_ENVIRONMENT_NAME}.yaml"

exec java ${JAVA_OPTS} -jar prebid-server.jar --spring.config.additional-location="${APPLICATION_CONFIG_FILE}"