FROM public.ecr.aws/docker/library/maven:3-amazoncorretto-21 AS build

RUN mkdir source && \
    yum install -y git && \
    git clone https://github.com/mbrtargeting/prebid-server-java.git source

WORKDIR source

RUN git fetch && git checkout 0f23f25f292665838c8983f2f239021be9bad8e1
RUN mvn dependency:resolve -T1.0C
RUN mvn dependency:resolve-plugins -T1.0C
RUN mvn package -Dmaven.test.skip


FROM public.ecr.aws/amazoncorretto/amazoncorretto:21

ENV APP_ROOT="/app"
ENV APP_PROPS_ROOT="${APP_ROOT}/props"

RUN mkdir -p ${APP_ROOT}/bin && \
    mkdir -p ${APP_ROOT}/stored/requests && \
    mkdir -p ${APP_ROOT}/stored/impressions && \
    mkdir -p ${APP_ROOT}/stored/responses && \
    chown -R nobody:nobody ${APP_ROOT}

COPY --from=build --chown=nobody:nobody source/target/prebid-server.jar ${APP_ROOT}
COPY --chown=nobody:nobody docker/run.sh ${APP_ROOT}/bin/
COPY --chown=nobody:nobody docker/version.json ${APP_ROOT}/
COPY --chown=nobody:nobody conf/* ${APP_PROPS_ROOT}/
COPY --chown=nobody:nobody stored-imps/ ${APP_ROOT}/stored/impressions/
RUN chmod +x ${APP_ROOT}/bin/run.sh

USER nobody
WORKDIR ${APP_ROOT}

ARG STROEER_VERSION=UNKNOWN
ENV STROEER_VERSION=${STROEER_VERSION}

CMD [ "bin/run.sh" ]