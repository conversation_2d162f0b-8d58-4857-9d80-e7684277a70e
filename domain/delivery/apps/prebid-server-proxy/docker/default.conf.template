server {
    server_tokens off;
    listen 23766;

    proxy_pass_request_headers on;
    proxy_pass_request_body on;

    proxy_ssl_verify ${PROXY_SSL_VERIFY};
    proxy_ssl_session_reuse on;
    proxy_ssl_server_name on;
    proxy_ssl_trusted_certificate /etc/ssl/certs/ca-certificates.crt;

    proxy_set_header Host $proxy_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

    # Assuming the domain is not being set.
    # HACK: Slipping Domain= in by modifying the Path=/; section of the cookie.
    # TODO: Have prebid server explicity set domain, then we can use the proxy_cookie_domain directive.
    proxy_cookie_path ~^(.+)$ "$1; Domain=${COOKIE_DOMAIN}";

    location /ready {
        default_type text/plain;
        return 200 "OK";
    }

    location /cookie_sync {
        proxy_set_header Accept-Encoding "";
        proxy_pass https://${PREBID_SERVER_DOMAIN}:443/cookie_sync;
        sub_filter '${PREBID_SERVER_DOMAIN}' '${PROXY_PREBID_SERVER_DOMAIN}';
        sub_filter_once off;
        sub_filter_types application/json text/plain;
    }

    location /setuid {
        # Need to support large cookie
        proxy_buffer_size 8k;
        proxy_pass https://${PREBID_SERVER_DOMAIN}:443/setuid;
    }

    location / {
        return 404;
    }
}
