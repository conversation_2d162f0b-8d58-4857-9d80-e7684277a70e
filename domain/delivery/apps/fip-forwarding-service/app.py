import logging
import os
import platform
import signal
import sys

import ecs_logging

from fip_forwarding_service.FipForwardingService import FipForwardingService

handler = logging.StreamHandler()
handler.setFormatter(ecs_logging.StdlibFormatter(
    extra={
        'host.hostname': platform.node(),
        'host.name': platform.node(),
        'host.architecture': platform.machine(),
        'service.node.name': platform.node(),
        'service.version': os.environ.get('STROEER_VERSION', 'UNKNOWN')
    }
))
handler.setLevel(logging.INFO)

logging.basicConfig(
    level=logging.INFO,
    handlers=[handler]
)

def terminate(signal,frame):
  logging.info("SIGTERM Received, shutting down...")
  sys.exit(0)

if __name__ == '__main__' and __package__ is None:
    signal.signal(signal.SIGTERM, terminate)

    config = FipForwardingService.Config()
    try:
        with FipForwardingService(config) as app:
            app.configure_kafka()
            app.serve_metrics()
            app.run()
    except KeyboardInterrupt:
        logging.info('I have been asked nicely to die')
    except Exception as e:
        logging.critical('Encountered a fatal unhandled exception that crashed the app: %s', e, exc_info=e)
        raise e
    finally:
        logging.info('(x_x) I am now dead')
