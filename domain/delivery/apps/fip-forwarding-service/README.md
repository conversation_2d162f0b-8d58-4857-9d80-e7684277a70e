# Fip Forwarding Service
This app is a microservice that receives the furl from Kafka and calls it to notify the partner.

## Explanation
For some traffic sources (especially PPV), we act as a proxy between the publisher and the DSP for handling tracking
URLs, including the display confirmation event. If a parameter called `furl` is present on a call to `trx.adscalde.de/fip`, we are expected to call the URL encoded in that parameter to notify the DSP partner that there 
was an ad impression. If we fail to notify the DSP, the DSP might never know the impression happened. For Stroer
Adserver PPV this is bad, because campaign pacing and budget relies on the Core DSP tracking app receiving the 
impression notification.

The `furl` used to be called directly by the `trx` inside the `fip` hot-path, but this meant that if there was ever a 
problem with the connection (such as DSP tracking hanging, or closing the connection), the impression call would be lost if the internal HTTP queue size was exceeded or the app restarted before clearing the queue. To make this more robust we
now send an `furl` notification on Kafka, and this app then goes through the topic calling the `furls`. This is less
liekly to result in lost notifications.

## Development
This is a Python app, built with `[poetry](https://python-poetry.org/)`. Make sure you have it installed, then just call `poetry build`.

