import logging
import time
from typing import List, Dict
from unittest.mock import MagicMock, Mock

from kafka.consumer.fetcher import ConsumerRecord

from fip_forwarding_service import FurlMessage
from fip_forwarding_service.FurlMessage import furl_message_serializer, furl_message_deserializer


class _MockKafkaConsumer:
    topics: List[str]

    def __init__(self, parent):
        self.parent = parent

    def __iter__(self):
        return self

    def __next__(self):
        for topic in self.topics:
            if topic in self.parent.MOCKED_TOPICS and self.parent.MOCKED_TOPICS[topic]:
                msg = self.parent.MOCKED_TOPICS[topic].pop(0)
                logging.info('Returning message from Kafka topic: %s: %s', topic, msg)
                return msg
            else:
                raise StopIteration()

    def subscribe(self, topics: List[str]):
        self.topics = topics

    def close(self):
        pass

    def metrics(self):
        return dict()


class _MockKafkaProducer:
    timestamp: int = None

    def __init__(self, parent):
        self.parent = parent

    def send(self, topic: str, value: FurlMessage, key: bytes = None):
        assert type(key) in (bytes, bytearray, memoryview, type(None))
        msg = furl_message_serializer(value)
        if topic not in self.parent.MOCKED_TOPICS:
            self.parent.MOCKED_TOPICS[topic] = []
        self.parent.MOCKED_TOPICS[topic].append(
            ConsumerRecord(
                topic=topic,
                partition=0,
                offset=len(self.parent.MOCKED_TOPICS[topic]),
                timestamp=int(time.time()) if self.timestamp is None else self.timestamp,
                timestamp_type=0,
                key=key,
                value=msg,
                headers=None,
                checksum=None,
                serialized_key_size=0,
                serialized_value_size=len(msg),
                serialized_header_size=0,
            )
        )

    def send_raw(self, topic: str, value: str, key: str = None):
        msg = value.encode('utf-8')
        if topic not in self.parent.MOCKED_TOPICS:
            self.parent.MOCKED_TOPICS[topic] = []
        self.parent.MOCKED_TOPICS[topic].append(
            ConsumerRecord(
                topic=topic,
                partition=0,
                offset=len(self.parent.MOCKED_TOPICS[topic]),
                timestamp=int(time.time()) if self.timestamp is None else self.timestamp,
                timestamp_type=0,
                key=key,
                value=msg,
                headers=None,
                checksum=None,
                serialized_key_size=0,
                serialized_value_size=len(msg),
                serialized_header_size=0,
            )
        )

    def close(self):
        pass

    def flush(self):
        pass

class MockKafka:

    MOCKED_TOPICS: Dict[str, List[ConsumerRecord]] = {}

    def __init__(self):
        self.consumer = _MockKafkaConsumer(self)
        self.producer = _MockKafkaProducer(self)

