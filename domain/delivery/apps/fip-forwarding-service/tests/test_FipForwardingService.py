import os
import re
import time
from unittest.mock import Mock, MagicMock
from unittest import mock

import httpretty
import pytest
from assertpy import assert_that
from prometheus_client import Gauge

from fip_forwarding_service.FipForwardingService import FipForwardingService
from fip_forwarding_service.FurlMessage import FurlMessage
from tests.mock_Kafka import Mock<PERSON><PERSON><PERSON>


def test_creating_app_config_defaults():
    os.environ.clear()
    config = FipForwardingService.Config()
    assert config.kafka_bootstrap_server == 'kafka.lsd.test:9092'
    assert config.input_kafka_topic == 'forwarded_fip_furls'
    assert config.quarantine_kafka_topic == 'forwarded_fip_furls_quarantine'
    assert config.discard_kafka_topic == 'forwarded_fip_furls_discard'
    assert config.kafka_auto_offset_reset == 'latest'

    assert config.prometheus_metrics_port == 8096

    assert config.http_read_timeout_seconds == 30
    assert config.http_connect_timeout_seconds == 30


def test_creating_app_config_env_var_overrides():
    os.environ.clear()
    os.environ['KAFKA_BOOTSTRAP_SERVER'] = 'SUCCESS'
    os.environ['KAFKA_TOPIC'] = 'SUCCESS'
    os.environ['KAFKA_AUTO_OFFSET_RESET'] = 'SUCCESS'
    os.environ['PROMETHEUS_METRICS_PORT'] = '8080'
    os.environ['HTTP_READ_TIMEOUT_SECONDS'] = '0'
    os.environ['HTTP_CONNECT_TIMEOUT_SECONDS'] = '0'

    config = FipForwardingService.Config()

    assert config.kafka_bootstrap_server == 'SUCCESS'
    assert config.input_kafka_topic == 'SUCCESS'
    assert config.kafka_auto_offset_reset == 'SUCCESS'

    assert config.prometheus_metrics_port == 8080

    assert config.http_read_timeout_seconds == 0
    assert config.http_connect_timeout_seconds == 0
    os.environ.clear()


@httpretty.activate(verbose=True, allow_net_connect=True)
def test_end_to_end(running_app, kafka_consumer, kafka_producer):
    httpretty.register_uri(
        httpretty.GET,
        re.compile(r'http://not.lsd.test/imp.*'),
    )

    kafka_producer.send('forwarded_fip_furls', FurlMessage(
        'impId',
        'http://not.lsd.test/imp',
        '666',
        'deal-1'
    ))

    running_app.run()

    requests = httpretty.latest_requests()

    assert_that(requests).is_length(1)

    request = requests.pop()

    assert_that(request.url).is_equal_to('http://not.lsd.test/imp')
    assert_that(request.method).is_equal_to('GET')
    assert_that(request.headers['User-Agent']).is_equal_to('Stroeer DOOH')


@httpretty.activate(verbose=True, allow_net_connect=False)
@mock.patch('time.time', MagicMock(return_value=1.247))
def test_attempts_exceeded_no_replay(running_app, kafka_consumer, kafka_producer, quarantine_producer):
    httpretty.register_uri(
        httpretty.GET,
        re.compile(r'http://not.lsd.test/imp.*'),
        status=500
    )

    kafka_producer.send('forwarded_fip_furls', FurlMessage(
        'impId',
        'http://not.lsd.test/imp',
        '666',
        'deal-1',
        5
    ))
    running_app.run()

    quarantine_producer.send.assert_called_with('forwarded_fip_furls_discard',
                                                value=FurlMessage(
                                                    'impId',
                                                    'http://not.lsd.test/imp',
                                                    '666',
                                                    'deal-1',
                                                    attempts=6,
                                                    createdTimestamp=1234,
                                                    elapsedMs=13,
                                                ),
                                                key='impId6'.encode('utf-8'))
    assert_that(httpretty.latest_requests()).is_length(1)
    running_app.metrics.http_errors.labels.assert_called_with('666', '500')
    running_app.metrics.http_furl_discarded.labels.assert_called_with('666')


@httpretty.activate(verbose=True, allow_net_connect=False)
def test_bad_message_with_incorrect_field_type_for_attempts(running_app,
                                                            kafka_consumer,
                                                            kafka_producer,
                                                            quarantine_producer,
                                                            ):
    """
    If part of our system starts populating the wrong types in messages to this app,
    we will effectively discard the malformed message.
    """
    httpretty.register_uri(
        httpretty.GET,
        re.compile(r'http://not.lsd.test/imp.*'),
        status=500
    )

    kafka_producer.send_raw('forwarded_fip_furls',
                            '{"impId": "impId", "furl": "http://not.lsd.test/imp", "dspId":"666", "dealId": "deal-1", "attempts": "abc"}')

    running_app.run()

    quarantine_producer.send.assert_not_called()
    assert_that(httpretty.latest_requests()).is_length(0)
    running_app.metrics.kafka_message_format_error.inc.assert_called_with()


@httpretty.activate(verbose=True, allow_net_connect=False)
def test_message_with_extra_field_is_attempted(running_app, kafka_consumer, kafka_producer, quarantine_producer):
    httpretty.register_uri(
        httpretty.GET,
        re.compile(r'http://not.lsd.test/imp.*'),
    )

    kafka_producer.send_raw('forwarded_fip_furls',
                            '{"impId": "impId", "furl": "http://not.lsd.test/imp", "dspId":"666", "dealId": "deal-1", "foobar": "abc", "bazbar": 1234, "barbaz": false}')

    running_app.run()
    requests = httpretty.latest_requests()

    quarantine_producer.send.assert_not_called()
    assert_that(requests).is_length(1)
    running_app.metrics.kafka_message_format_error.inc.assert_not_called()

    request = requests.pop()

    assert_that(request.url).is_equal_to('http://not.lsd.test/imp')
    assert_that(request.method).is_equal_to('GET')
    assert_that(request.headers['User-Agent']).is_equal_to('Stroeer DOOH')


@httpretty.activate(verbose=True, allow_net_connect=False)
def test_bad_message_with_missing_field(running_app, kafka_consumer, kafka_producer, quarantine_producer):
    httpretty.register_uri(
        httpretty.GET,
        re.compile(r'http://not.lsd.test/imp.*'),
        status=500
    )

    kafka_producer.send_raw('forwarded_fip_furls',
                            '{"impId": "impId", "dspId":"666", "dealId": "deal-1"}')

    running_app.run()

    quarantine_producer.send.assert_not_called()
    assert_that(httpretty.latest_requests()).is_length(0)
    running_app.metrics.kafka_message_format_error.inc.assert_called_with()


@httpretty.activate(verbose=True, allow_net_connect=False)
def test_bad_message_is_pure_garbage(running_app, kafka_consumer, kafka_producer, quarantine_producer):
    httpretty.register_uri(
        httpretty.GET,
        re.compile(r'http://not.lsd.test/imp.*'),
        status=500
    )

    kafka_producer.send_raw('forwarded_fip_furls',
                            'trump2024')

    running_app.run()

    quarantine_producer.send.assert_not_called()
    assert_that(httpretty.latest_requests()).is_length(0)
    running_app.metrics.kafka_message_format_error.inc.assert_called_with()


@httpretty.activate(verbose=True, allow_net_connect=False)
def test_bad_attempts_replay(running_app, kafka_consumer, kafka_producer, quarantine_producer):
    httpretty.register_uri(
        httpretty.GET,
        re.compile(r'http://not.lsd.test/imp.*'),
        status=500
    )

    kafka_producer.send('forwarded_fip_furls', FurlMessage(
        'impId',
        'http://not.lsd.test/imp',
        '666',
        'deal-1',
        None
    ))

    running_app.run()

    quarantine_producer.send.assert_called_with('forwarded_fip_furls_quarantine',
                                                value=FurlMessage(
                                                    'impId',
                                                    'http://not.lsd.test/imp',
                                                    '666',
                                                    'deal-1',
                                                    attempts=1,
                                                    createdTimestamp=1234,
                                                ),
                                                key='impId1'.encode('utf-8'))
    assert_that(httpretty.latest_requests()).is_length(1)


@pytest.mark.parametrize('protocol', ['http', 'https'])
def test_destination_address_not_resolved(running_app, kafka_consumer, kafka_producer, quarantine_producer, protocol):
    kafka_producer.send('forwarded_fip_furls', FurlMessage(
        'impId',
        protocol + '://not.lsd.test/imp',
        '666',
        'deal-1'
    ))

    running_app.run()

    quarantine_producer.send.assert_called_with('forwarded_fip_furls_quarantine',
                                                value=FurlMessage(
                                                    'impId',
                                                    protocol + '://not.lsd.test/imp',
                                                    '666',
                                                    'deal-1',
                                                    attempts=1,
                                                    createdTimestamp=1234,
                                                ),
                                                key='impId1'.encode('utf-8'))


@httpretty.activate(verbose=True, allow_net_connect=False)
def test_redirect(running_app, kafka_consumer, kafka_producer):
    httpretty.register_uri(
        httpretty.GET,
        re.compile(r'http://not.lsd.test/imp.*'),
        status=302,
        adding_headers={
            'Location': 'https://not.lsd.test/imp'
        }
    )

    httpretty.register_uri(
        httpretty.GET,
        re.compile(r'https://not.lsd.test/imp.*'),
    )

    kafka_producer.send('forwarded_fip_furls', FurlMessage(
        'impId',
        'http://not.lsd.test/imp',
        '666',
        'deal-1'
    ))

    running_app.run()

    assert_that(httpretty.latest_requests()).is_length(2)
    running_app.metrics.http_successes.labels.assert_called_with('666')


@httpretty.activate(verbose=True, allow_net_connect=False)
def test_infinite_redirects_are_stopped(running_app, kafka_consumer, kafka_producer):
    httpretty.register_uri(
        httpretty.GET,
        re.compile(r'http://not.lsd.test/imp.*'),
        status=302,
        adding_headers={
            'Location': 'http://not.lsd.test/imp'
        }
    )

    kafka_producer.send('forwarded_fip_furls', FurlMessage(
        'impId',
        'http://not.lsd.test/imp',
        '666',
        'deal-1'
    ))

    running_app.run()

    assert_that(httpretty.latest_requests()).is_length(3)
    running_app.metrics.http_too_many_redirects.labels.assert_called_with('666')


@httpretty.activate(verbose=True, allow_net_connect=False)
def test_read_timeout(running_app, kafka_consumer, kafka_producer, quarantine_producer):
    def timeout(*args, **kwargs):
        time.sleep(6)
        return 504, {}, 'timeout'

    httpretty.register_uri(
        httpretty.GET,
        re.compile(r'http://not.lsd.test/imp.*'),
        body=timeout,
    )

    kafka_producer.send('forwarded_fip_furls', FurlMessage(
        'impId',
        'http://not.lsd.test/imp',
        '666',
        'deal-1'
    ))

    running_app.run()

    assert_that(httpretty.latest_requests()).is_length(1)
    quarantine_producer.send.assert_called_with('forwarded_fip_furls_quarantine',
                                                value=FurlMessage(
                                                    'impId',
                                                    'http://not.lsd.test/imp',
                                                    '666',
                                                    'deal-1',
                                                    attempts=1,
                                                    createdTimestamp=1234,
                                                ),
                                                key='impId1'.encode('utf-8'))
    running_app.metrics.http_read_timeout.labels.assert_called_with('666')


@httpretty.activate(verbose=True, allow_net_connect=False)
def test_slow_dsp_message_quarantined_without_attempt(running_app, kafka_consumer, kafka_producer, quarantine_producer):
    httpretty.register_uri(
        httpretty.GET,
        re.compile(r'http://not.lsd.test/imp.*'),
    )
    running_app.metrics.http_request_time_recent_seconds.labels('666')._value.get.return_value = 6.2

    kafka_producer.send('forwarded_fip_furls', FurlMessage(
        'impId',
        'http://not.lsd.test/imp',
        '666',
        'deal-1'
    ))

    running_app.run()

    assert_that(httpretty.latest_requests()).is_length(0)
    quarantine_producer.send.assert_called_with('forwarded_fip_furls_quarantine',
                                                value=FurlMessage(
                                                    'impId',
                                                    'http://not.lsd.test/imp',
                                                    '666',
                                                    'deal-1',
                                                    attempts=1,
                                                    createdTimestamp=1234,
                                                ),
                                                key='impId1'.encode('utf-8'))


@httpretty.activate(verbose=True, allow_net_connect=False)
def test_quarantine_processor_handles_all_quarantine_messages_ignores_dsp_response_time(running_app, kafka_consumer,
                                                                                        kafka_producer,
                                                                                        quarantine_producer):
    httpretty.register_uri(
        httpretty.GET,
        re.compile(r'http://not.lsd.test/imp.*'),
    )
    running_app.config.quarantine_processor = True
    running_app.metrics.http_request_time_recent_seconds.labels('666')._value.get.return_value = 6.2

    kafka_producer.send('forwarded_fip_furls_quarantine', FurlMessage(
        'impId',
        'http://not.lsd.test/imp',
        '666',
        'deal-1'
    ))

    running_app.run()

    assert_that(httpretty.latest_requests()).is_length(1)
    quarantine_producer.send.assert_not_called()


@httpretty.activate(verbose=True, allow_net_connect=False)
def test_sample_one_percent_of_slow_messages_to_update_dsp_response_times(running_app, kafka_consumer, kafka_producer,
                                                                          quarantine_producer):
    httpretty.register_uri(
        httpretty.GET,
        re.compile(r'http://not.lsd.test/imp.*'),
    )
    running_app.metrics.http_request_time_recent_seconds.labels('666')._value.get.return_value = 6.2

    # impression id 'impId50' hashes to 0 so that the percentage check passes 
    kafka_producer.send('forwarded_fip_furls', FurlMessage(
        'impId50',
        'http://not.lsd.test/imp',
        '666',
        'deal-1'
    ))

    running_app.run()

    quarantine_producer.send.assert_not_called()
    assert_that(httpretty.latest_requests()).is_length(1)


@httpretty.activate(verbose=True, allow_net_connect=False)
def test_no_cookie_param_added_for_mbr(running_app, kafka_consumer, kafka_producer):
    httpretty.register_uri(
        httpretty.GET,
        re.compile(r'http://not.lsd.test/imp.*')
    )

    kafka_producer.send('forwarded_fip_furls', FurlMessage(
        'impId',
        'http://not.lsd.test/imp',
        '48',
        'deal-1'
    ))

    running_app.run()

    assert_that(httpretty.latest_requests()).is_length(1)
    assert_that(httpretty.last_request().url).ends_with('&nocookie=true')
    running_app.metrics.http_successes.labels.assert_called_with('48')


@httpretty.activate(verbose=True, allow_net_connect=False)
@pytest.mark.parametrize('status_code', [500, 503, 504, 400, 404])
def test_http_status_codes_trigger_quarantine(running_app, kafka_consumer, kafka_producer, quarantine_producer,
                                              status_code: int):
    httpretty.register_uri(
        httpretty.GET,
        re.compile(r'http://not.lsd.test/imp.*'),
        status=status_code
    )

    kafka_producer.send('forwarded_fip_furls', FurlMessage(
        'impId',
        'http://not.lsd.test/imp',
        '666',
        'deal-1'
    ))

    running_app.run()

    quarantine_producer.send.assert_called_with('forwarded_fip_furls_quarantine',
                                                value=FurlMessage(
                                                    'impId',
                                                    'http://not.lsd.test/imp',
                                                    '666',
                                                    'deal-1',
                                                    attempts=1,
                                                    createdTimestamp=1234,
                                                ),
                                                key='impId1'.encode('utf-8'))
    assert_that(httpretty.latest_requests()).is_length(1)

    running_app.metrics.http_errors.labels.assert_called_with('666', str(status_code))


@pytest.fixture(scope='function')
def mock_kafka():
    yield MockKafka()


@pytest.fixture(scope='function')
def kafka_consumer(mock_kafka):
    yield mock_kafka.consumer


@pytest.fixture(scope='function')
def kafka_producer(mock_kafka):
    yield mock_kafka.producer


@pytest.fixture(scope='function')
def quarantine_producer(mock_kafka):
    yield Mock(wraps=mock_kafka.producer)


@pytest.fixture(scope='function')
def running_app(mock_kafka, kafka_consumer, quarantine_producer):
    app = FipForwardingService(FipForwardingService.Config(
        quarantine_kafka_topic='forwarded_fip_furls_quarantine',
        http_read_timeout_seconds=5,
    ))
    app.kafka_consumer = kafka_consumer
    app.kafka_producer = quarantine_producer
    app.metrics = MagicMock()
    app.metrics.http_request_time_recent_seconds.labels('666')._value.get.return_value = 0.2
    mock_kafka.MOCKED_TOPICS.clear()
    mock_kafka.producer.timestamp = 1234
    yield app
    app.close()
