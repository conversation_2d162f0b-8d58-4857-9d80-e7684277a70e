import dataclasses
import json
from typing import Optional

from dacite import from_dict


@dataclasses.dataclass
class FurlMessage:
    impId: str
    furl: str
    dspId: str
    dealId: Optional[str] = None
    attempts: Optional[int] = 0
    createdTimestamp: Optional[int] = None
    elapsedMs: Optional[int] = None
    displayPixelId: Optional[str] = None

    def as_dict(self) -> dict:
        return {
            'impId': self.impId,
            'furl': self.furl,
            'dspId': self.dspId,
            'dealId': self.dealId,
            'attempts': self.attempts,
            'createdTimestamp': self.createdTimestamp,
            'elapsedMs': self.elapsedMs,
            'displayPixelId': self.displayPixelId,
        }


def furl_message_deserializer(raw_message: bytes) -> FurlMessage:
    return json.loads(raw_message.decode('utf-8'), object_hook=lambda d: from_dict(data_class=FurlMessage, data=d))


def furl_message_serializer(message: FurlMessage) -> bytes:
    return json.dumps(message.as_dict()).encode('utf-8')


