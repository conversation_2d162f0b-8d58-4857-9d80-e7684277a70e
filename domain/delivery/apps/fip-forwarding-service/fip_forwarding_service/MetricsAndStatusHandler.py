from urllib.parse import parse_qs, urlparse

from prometheus_client import MetricsHandler


class MetricsAndStatusHandler(MetricsHandler):

    def do_GET(self) -> None:
        path = urlparse(self.path).path
        if path == '/up':
            self.protocol_version = 'HTTP/1.1'
            self.send_response(200)
            self.end_headers()
            self.wfile.write('OK'.encode('utf-8'))
        else:
            super().do_GET()
