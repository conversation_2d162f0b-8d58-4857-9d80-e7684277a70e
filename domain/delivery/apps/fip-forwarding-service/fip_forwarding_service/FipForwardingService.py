import dataclasses
import http.server
import os
import time
import socket
import threading
from dataclasses import field
from functools import partial
from json import JSONDecodeError

import kafka
import requests
from dacite import MissingValueError, WrongTypeError
from kafka import KafkaConsumer, KafkaProducer
from prometheus_client import Summary, REGISTRY, Counter, Gauge
from requests.adapters import HTTPAdapter

from fip_forwarding_service import module_logger
from fip_forwarding_service.FurlMessage import furl_message_deserializer, FurlMessage, furl_message_serializer
from fip_forwarding_service.KafkaMetricCollector import KafkaMetricCollector
from fip_forwarding_service.MetricsAndStatusHandler import MetricsAndStatusHandler


class FipForwardingService:
    _class_logger = module_logger.getChild(__qualname__)

    REQUEST_TIME = Summary('request_processing_seconds', 'Time spent processing requests')


    @dataclasses.dataclass
    class Config:
        def _get_int_environ(name: str, default: int):
            return int(os.environ.get(name, default))

        def _get_float_environ(name: str, default: float):
            return float(os.environ.get(name, default))

        def _get_bool_environ(name: str, default: str):
            return os.environ.get(name, default).lower() in ['true', '1', 't', 'y'] 

        kafka_bootstrap_server: str = field(
            default_factory=partial(os.environ.get, 'KAFKA_BOOTSTRAP_SERVER', 'kafka.lsd.test:9092'))
        input_kafka_topic: str = field(
            default_factory=partial(os.environ.get, 'KAFKA_TOPIC', 'forwarded_fip_furls'))
        quarantine_kafka_topic: str = field(
            default_factory=partial(os.environ.get, 'QUARANTINE_KAFKA_TOPIC', 'forwarded_fip_furls_quarantine'))
        discard_kafka_topic: str = field(
            default_factory=partial(os.environ.get, 'DISCARD_KAFKA_TOPIC', 'forwarded_fip_furls_discard'))
        kafka_auto_offset_reset: str = field(
            default_factory=partial(os.environ.get, 'KAFKA_AUTO_OFFSET_RESET', 'latest'))
        kafka_session_timeout_ms: int = field(
            default_factory=partial(_get_int_environ, 'SESSION_TIMEOUT_MS', 45000))
        kafka_max_poll_records: int = field(
            default_factory=partial(_get_int_environ, 'MAX_POLL_RECORDS', 10))

        quarantine_processor: bool = field(
            default_factory=partial(_get_bool_environ, 'QUARANTINE_PROCESSOR', 'false'))
        quarantine_max_retries: int = field(
            default_factory=partial(_get_int_environ, 'QUARANTINE_MAX_RETRIES', 5))
        quarantine_response_time_threshold: float = field(
            default_factory=partial(_get_float_environ, 'QUARANTINE_RESPONSE_TIME_THRESHOLD', 0.500))
        quarantine_sample_percentage: int = field(
            default_factory=partial(_get_int_environ, 'QUARANTINE_SAMPLE_PERCENTAGE', 1))

        http_connect_timeout_seconds: int = field(
            default_factory=partial(_get_int_environ, 'HTTP_CONNECT_TIMEOUT_SECONDS', 30))
        http_read_timeout_seconds: int = field(
            default_factory=partial(_get_int_environ, 'HTTP_READ_TIMEOUT_SECONDS', 30))
        http_max_connections: int = field(
            default_factory=partial(_get_int_environ, 'HTTP_MAX_CONNECTIONS', 100))
        http_max_redirects: int = field(
            default_factory=partial(_get_int_environ, 'HTTP_MAX_REDIRECTS', 2))

        prometheus_metrics_port: int = field(
            default_factory=partial(_get_int_environ, 'PROMETHEUS_METRICS_PORT', 8096))

    class Metrics:
        http_successes = Counter('http_client_success',
                                 'Total number of successful calls made using the HTTP client',
                                 ('dsp_partner_id',),
                                 )

        http_errors = Counter('http_client_error',
                              'Total number of bad HTTP responses received by the HTTP client',
                              ('dsp_partner_id', 'status_code'),
                              )

        http_read_timeout = Counter('http_client_timeout',
                                    'Total number of timed-out calls made using the HTTP client',
                                    ('dsp_partner_id',),
                                    )

        http_connection_errors = Counter('http_client_connection_errors',
                                         'Total number of connection failures from the HTTP client',
                                         ('dsp_partner_id',),
                                         )

        http_too_many_redirects = Counter('http_client_too_many_redirects',
                                          'Total number of redirect errors from the HTTP client',
                                          ('dsp_partner_id',),
                                          )

        http_unknown_exceptions = Counter('http_client_unknown_exception',
                                          'Total number of unknown errors from the HTTP client',
                                          ('dsp_partner_id',),
                                          )

        http_furl_discarded = Counter('http_client_furl_discarded',
                                      'Total number of furls discarded due to exceeding retry threshold',
                                      ('dsp_partner_id',),
                                      )

        http_furl_quarantined = Counter('http_client_furl_quarantined',
                                      'Total number of furls quarantined due to exceeding response time threshold',
                                      ('dsp_partner_id',),
                                      )

        http_request_time_seconds = Summary('http_request_time_seconds',
                                            'Total time spent making HTTP calls to DSP partner',
                                            ('dsp_partner_id',),
                                            )

        http_request_time_recent_seconds = Gauge('http_request_time_recent_seconds',
                                                 'Time of most recent HTTP request to DSP',
                                                 ('dsp_partner_id',),
                                                 )

        kafka_message_format_error = Counter('kafka_input_message_deserialization_error',
                                             'Total number of Kafka records that could not be decoded',
                                             )

    metrics: Metrics = None

    _metrics_server: http.server.HTTPServer = None

    kafka_consumer: kafka.KafkaConsumer = None
    kafka_producer: kafka.KafkaProducer = None

    def __init__(self,
                 config: Config):

        self.log = self._class_logger.getChild(str(id(self)))

        self.config = config

        self.http_session = requests.Session()
        self.http_session.max_redirects = self.config.http_max_redirects

        adapter = HTTPAdapter(pool_maxsize=self.config.http_max_connections)
        self.http_session.mount('http://', adapter)
        self.http_session.mount('https://', adapter)

        self.timeout = (self.config.http_connect_timeout_seconds, self.config.http_read_timeout_seconds)


    def _quarantine(self, message: FurlMessage):
        message.attempts = message.attempts + 1 if type(message.attempts) == int else 1
        if message.attempts > self.config.quarantine_max_retries:
            self.log.error('Exceeded max retries (%s) for a message giving up!: %s',
                           self.config.quarantine_max_retries, message)
            self.metrics.http_furl_discarded.labels(message.dspId).inc()
            message.elapsedMs = (time.time() * 1000) - message.createdTimestamp
            self.kafka_producer.send(self.config.discard_kafka_topic,
                                    value=message,
                                    key=bytes(message.impId + str(message.attempts), 'utf-8')
                                    )
            return
        self.kafka_producer.send(self.config.quarantine_kafka_topic,
                                 value=message,
                                 key=bytes(message.impId + str(message.attempts), 'utf-8')
                                 )

    def _should_quarantine_slow_dsps(self, dspId: str, impId: str) -> bool:
        # sample some percent of slow messages to update response time metrics
        # for easy testing will base it on impression id
        # can't use hash() as seeding is randomized when python starts
        return self.metrics.http_request_time_recent_seconds.labels(dspId)._value.get() > self.config.quarantine_response_time_threshold \
        and not int.from_bytes(impId.encode(), 'big') % 100 < self.config.quarantine_sample_percentage


    def _handle_message(self, message: FurlMessage):
        try:
            dspId = message.dspId
            impId = message.impId
            if not self.config.quarantine_processor and self._should_quarantine_slow_dsps(dspId, impId):
                self.metrics.http_furl_quarantined.labels(dspId).inc()
                self._quarantine(message)
            else:
                self._attempt_message_send(message)
        except Exception as e:
            self.log.error('Exception handling message: %s', e, exc_info=e)
            self.metrics.http_unknown_exceptions.labels(dspId).inc()


    @REQUEST_TIME.time()
    def _attempt_message_send(self, message: FurlMessage):
        self.log.debug('Handling furl message from Kafka')
        with self.metrics.http_request_time_seconds.labels(message.dspId).time(), self.metrics.http_request_time_recent_seconds.labels(message.dspId).time():
            try:
                if message.dspId == '48' and 'nocookie' not in message.furl:
                    message.furl += '&nocookie=true'
                response = self.http_session.get(message.furl,
                                                 timeout=self.timeout,
                                                 headers={'User-Agent': 'Stroeer DOOH'},
                                                 )
                response.raise_for_status()
                self.metrics.http_successes.labels(message.dspId).inc()
            except requests.exceptions.ReadTimeout:
                self.log.warning('Timeout while attempting to call URL: %s', message)
                self.metrics.http_read_timeout.labels(message.dspId).inc()
                self._quarantine(message)
            except requests.exceptions.TooManyRedirects:
                self.log.warning('Too many redirects while attempting to call URL: %s', message)
                self.metrics.http_too_many_redirects.labels(message.dspId).inc()
                self._quarantine(message)
            except requests.exceptions.ConnectionError as e:
                self.log.warning('Connection error (%s) while attempting to call URL: %s', e, message)
                self.metrics.http_connection_errors.labels(message.dspId).inc()
                self._quarantine(message)
            except requests.exceptions.HTTPError as e:
                self.log.warning('HTTPError (%s) while attempting to call URL: %s', e, message)
                self.metrics.http_errors.labels(message.dspId, str(e.response.status_code)).inc()
                self._quarantine(message)
            except Exception as e:
                self.log.error('Exception calling endpoint: %s %s', e, message, exc_info=e)
                self.metrics.http_unknown_exceptions.labels(message.dspId).inc()
                self._quarantine(message)

    def configure_kafka(self):
        if self.kafka_consumer is None:
            self.kafka_consumer = KafkaConsumer(
                bootstrap_servers=self.config.kafka_bootstrap_server,
                group_id='fip_forwarding_service',
                client_id='consumer-' + socket.getfqdn(),  # should be pod_id in prod
                auto_offset_reset=self.config.kafka_auto_offset_reset,
                session_timeout_ms=self.config.kafka_session_timeout_ms,
                max_poll_records=self.config.kafka_max_poll_records,
            )

        if self.kafka_producer is None:
            self.kafka_producer = KafkaProducer(
                bootstrap_servers=self.config.kafka_bootstrap_server,
                client_id='quarantine-producer-' + socket.getfqdn(),  # should be pod_id in prod
                value_serializer=furl_message_serializer,
                acks=1,
                linger_ms=1000,
            )

        self.kafka_producer.partitions_for(self.config.input_kafka_topic)
        self.kafka_producer.partitions_for(self.config.quarantine_kafka_topic)

    def serve_metrics(self):
        self.metrics = self.Metrics()
        kafka_metrics = KafkaMetricCollector(self.kafka_consumer)
        REGISTRY.register(kafka_metrics)
        self.log.info('Successfully registered KafkaMetricCollector with prometheus')
        self.log.info('Setting up Prometheus metrics server on port %s', self.config.prometheus_metrics_port)
        self._metrics_server = http.server.HTTPServer(
            ('0.0.0.0', int(self.config.prometheus_metrics_port)),
            MetricsAndStatusHandler,
        )
        t = threading.Thread(target=self._metrics_server.serve_forever)
        t.daemon = True
        t.start()
        self.log.info('Successfully started Prometheus metrics server')

    def run(self):
        topic = self.config.input_kafka_topic
        if self.config.quarantine_processor:
            self.log.info('Starting as quarantine processor')
            topic = self.config.quarantine_kafka_topic
        self.log.info('Subscribing to Kafka topic \'%s\'', topic)
        self.kafka_consumer.subscribe([topic])
        self.log.info('Subscribed to Kafka topic \'%s\'', topic)

        for message in self.kafka_consumer:
            try:
                self.log.debug('Received a message from Kafka')
                value: bytes = message.value
                deserialized_msg = furl_message_deserializer(value)
                deserialized_msg.createdTimestamp = message.timestamp
                self._handle_message(deserialized_msg)
            except (TypeError, JSONDecodeError, MissingValueError, WrongTypeError) as e:
                self.log.error('Exception decoding message from input Kafka topic: %s', e, exc_info=e)
                self.metrics.kafka_message_format_error.inc()

    def close(self):
        if self.kafka_consumer is not None:
            self.log.info('close kafka consumer')
            self.kafka_consumer.close()
        if self.kafka_producer is not None:
            self.log.info('flush and close kafka producer')
            self.kafka_producer.flush()
            self.kafka_producer.close()
        if self._metrics_server:
            self.log.info('shutdown metrics server')
            self._metrics_server.shutdown()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.log.info('close fip forwarding service app')
        self.close()
