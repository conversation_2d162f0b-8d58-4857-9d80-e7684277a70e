from kafka import KafkaConsumer
from prometheus_client.core import GaugeMetricFamily


class KafkaMetricCollector:
    def __init__(self, kafka_consumer: KafkaConsumer):
        self.kafka_consumer = kafka_consumer

    def collect(self):
        metrics = self.kafka_consumer.metrics()
        if 'kafka-metrics-count' in metrics:
            del metrics['kafka-metrics-count']
        for blob in metrics.values():
            for name, value in blob.items():
                yield GaugeMetricFamily(name=f'kafka_consumer_{name.replace("-", "_")}', documentation='', value=value)
