[tool.poetry]
name = "fip-forwarding-service"
version = "0.1.0"
description = ""
authors = ["<PERSON> <<EMAIL>>"]
readme = "README.md"
packages = [{include = "fip_forwarding_service"}]

[tool.poetry.dependencies]
python = "^3.9"
requests = "^2.31.0"
kafka-python = "^2.0.2"
prometheus-client = "^0.17.1"
ecs-logging = "^2.0.2"
assertpy = "^1.1"
zstandard = "^0.21.0"
dacite = "^1.8.1"


[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
httpretty = "^1.1.4"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
