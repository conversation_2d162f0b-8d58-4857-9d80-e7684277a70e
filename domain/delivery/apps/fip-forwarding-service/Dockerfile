FROM public.ecr.aws/docker/library/python:3.11.6-alpine as builder

RUN apk add --no-cache build-base libffi-dev

RUN pip install --upgrade pip && pip install poetry==1.5.1

WORKDIR /app
RUN python -m poetry config virtualenvs.create false

COPY poetry.lock pyproject.toml ./
RUN python -m poetry install --no-cache --no-directory --no-root --only main

FROM python:3.11.6-alpine

WORKDIR /app
COPY --from=builder /usr/local/lib/python3.11/site-packages/ /usr/local/lib/python3.11/site-packages/
COPY . .

ARG STROEER_VERSION=UNKNOWN
ENV STROEER_VERSION=${STROEER_VERSION}
ENV PROMETHEUS_DISABLE_CREATED_SERIES=True  

ENTRYPOINT ["python", "app.py"]