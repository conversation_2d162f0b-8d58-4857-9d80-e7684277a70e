function loadCss() {
    var head  = document.getElementsByTagName('head')[0];
    var link  = document.createElement('link');
    var advert = document.getElementById("mocks-advert");
    link.rel  = 'stylesheet';
    link.type = 'text/css';
    link.href = advert.getAttribute("data-css-link");
    link.media = 'all';

    head.appendChild(link);
}

function createAdContainer() {
    var advert = document.getElementById("mocks-advert");
    var div = document.createElement("div");
    div.className = "ad";
    div.style.height=advert.getAttribute("data-height") + "px";
    div.style.width=advert.getAttribute("data-width") + "px";

    var text = document.createElement("div");
    text.className = "bounce bouncy";
    var content = advert.getAttribute("data-content");
    text.appendChild(document.createTextNode(content));

    div.appendChild(text);
    advert.parentNode.insertBefore(div, advert.nextElementSibling);
}

loadCss();
createAdContainer();