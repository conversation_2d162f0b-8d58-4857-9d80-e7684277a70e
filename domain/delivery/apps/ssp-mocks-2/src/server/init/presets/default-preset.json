{"fileName": "default-preset.json", "description": "This preset contains all partner information as it was originally defined in DefaultPartners", "partners": [{"name": "<PERSON><PERSON><PERSON>", "endpoint": "http://__HOST__DOMAIN__:__PORT__/rtb/metrigo/bid", "requests": 0, "responses": [{"prebidServer": true, "conditional": false, "conditionalFunc": "function shouldRespond(request) {return true;}", "statusCode": 200, "name": "Prebid Server Response", "response": {"id": "10", "bidid": "1234", "cur": "EUR", "customdata": "some custom data", "seatbid": [{"bid": [{"id": "5678", "impid": "1", "price": "3.50", "adid": "9281", "adm": "<script id='mocks-advert' data-content='Response from backend prebid server demand. Metrigo __WIDTH__x__HEIGHT__ Price macros clear: ${AUCTION_PRICE} encrypted: ${AUCTION_PRICE:ENC} TCF Macros applies: ${GDPR} personal-data: ${GDPR_PD}' data-width=__WIDTH__ data-height=__HEIGHT__ data-css-link='https://__HOST__/ad.css' src='https://__HOST__/advert.js'></script>", "crid": "creative id1", "w": "__WIDTH__", "h": "__HEIGHT__", "adomain": ["domain1.com", "domain2.com"]}]}]}}, {"prebidServer": false, "conditional": true, "conditionalFunc": "function shouldRespond(request) { \n    return request.imp[0].video === undefined; \n}", "statusCode": 200, "name": "Default Response", "response": {"id": "393", "bidid": "8284", "cur": "US", "customdata": "some custom data", "seatbid": [{"group": 0, "bid": [{"id": "bidder id1", "impid": "1", "price": "5.57", "adid": "2822", "adm": "<script id='mocks-advert' data-content='Metrigo __WIDTH__x__HEIGHT__ Price macros clear: ${AUCTION_PRICE} encrypted: ${AUCTION_PRICE:ENC} TCF Macros applies: ${GDPR} personal-data: ${GDPR_PD}' data-width=__WIDTH__ data-height=__HEIGHT__ data-css-link='https://__HOST__/ad.css' src='https://__HOST__/advert.js'></script>", "crid": "creative id1", "w": "__WIDTH__", "h": "__HEIGHT__", "adomain": ["domain1.com", "domain2.com"], "ext": {"avn": "metrigoAvn", "agn": "metrigoAgn"}}]}]}}, {"prebidServer": false, "conditional": true, "conditionalFunc": "function shouldRespond(request) { \n return request?.site?.domain === 't-online.de' || request?.ext?.labels?.some(label => label === 'adpod'); \n}", "statusCode": 200, "name": "AdPod Response", "response": {"id": "video2-<PERSON><PERSON><PERSON>(991)", "bidid": "8284", "cur": "EUR", "customdata": "some custom data", "seatbid": [{"group": 0, "seat": "12345", "bid": [{"id": "Metrigo bid id2", "impid": "1", "price": "11.98", "adid": "2822", "adm": "<VAST version=\"2.0\"><Ad id=\"12345\"><InLine><AdSystem>Adscale</AdSystem><AdTitle>Mountebank preroll</AdTitle><Impression>http://static.steelsky.co.nz</Impression><Creatives><Creative><Linear><Duration>00:00:10</Duration><VideoClicks><ClickThrough>http://www.advert3.com</ClickThrough></VideoClicks><MediaFiles><MediaFile height=\"608\" width=\"1080\" type=\"video/mp4\" delivery=\"progressive\">https://s3-ap-southeast-2.amazonaws.com/adscale-test-ads/advert2.mp4</MediaFile></MediaFiles><TrackingEvents><Tracking event=\"start\">http://www.steelsky.co.nz/vastEvent?start=1</Tracking><Tracking event=\"complete\">http://www.steelsky.co.nz/vastEvent?complete=1</Tracking><Tracking event=\"firstQuartile\">http://www.steelsky.co.nz/vastEvent?firstQuartile=1</Tracking><Tracking event=\"midpoint\">http://www.steelsky.co.nz/vastEvent?midpoint=1</Tracking><Tracking event=\"thirdQuartile\">http://www.steelsky.co.nz/vastEvent?thirdQuartile=1</Tracking></TrackingEvents></Linear></Creative></Creatives></InLine></Ad></VAST>", "crid": "Metrigo Video crid2", "adomain": ["domain2.com"], "ext": {"avn": "MetrigoVideoAvn", "agn": "MetrigoVideoAgn", "at": 1}}]}]}}, {"prebidServer": false, "conditional": true, "conditionalFunc": "function shouldRespond(request) { \n    return request.site.id !== \"51303\" && request.imp[0].tagid !== \"41670\";\n}", "statusCode": 200, "name": "Programmatic Guaranteed Response", "response": {"id": "393", "bidid": "8284", "cur": "US", "customdata": "some custom data", "seatbid": [{"group": 0, "bid": [{"id": "bidder id1", "impid": "1", "cid": "ppv-trigger", "price": "5.57", "adid": "2822", "adm": "<script id='mocks-advert' data-content='Metrigo __WIDTH__x__HEIGHT__ Price macros clear: ${AUCTION_PRICE} encrypted: ${AUCTION_PRICE:ENC} TCF Macros applies: ${GDPR} personal-data: ${GDPR_PD}' data-width=__WIDTH__ data-height=__HEIGHT__ data-css-link='https://__HOST__/ad.css' src='https://__HOST__/advert.js'></script>", "crid": "creative id1", "w": "__WIDTH__", "h": "__HEIGHT__", "adomain": ["domain1.com", "domain2.com"], "ext": {"avn": "metrigoAvn", "agn": "metrigoAgn", "exchangeReference": {"token": "ppv-token"}}}]}]}}, {"prebidServer": false, "conditional": true, "conditionalFunc": "function shouldRespond(request) { \n    return request.site.id !== \"51303\";\n}", "statusCode": 200, "name": "PPV IO Response", "response": {"id": "393", "bidid": "8284", "cur": "US", "customdata": "some custom data", "seatbid": [{"group": 0, "bid": [{"id": "bidder id1", "impid": "1", "cid": "ppv-io-trigger", "price": "15.57", "adid": "2822", "adm": "<VAST version=\"2.0\"><Ad id=\"12345\"><InLine><AdSystem>Adscale</AdSystem><AdTitle>Mountebank preroll</AdTitle><Impression>http://static.steelsky.co.nz?cost=${AUCTION_PRICE}</Impression><Creatives><Creative><Linear><Duration>00:00:30</Duration><VideoClicks><ClickThrough>http://www.advert3.com</ClickThrough></VideoClicks><MediaFiles><MediaFile height=\"608\" width=\"1080\" type=\"video/mp4\" delivery=\"progressive\">https://s3-ap-southeast-2.amazonaws.com/adscale-test-ads/advert2.mp4</MediaFile></MediaFiles><TrackingEvents><Tracking event=\"start\">http://www.steelsky.co.nz/vastEvent?start=1</Tracking><Tracking event=\"complete\">http://www.steelsky.co.nz/vastEvent?complete=1</Tracking><Tracking event=\"firstQuartile\">http://www.steelsky.co.nz/vastEvent?firstQuartile=1</Tracking><Tracking event=\"midpoint\">http://www.steelsky.co.nz/vastEvent?midpoint=1</Tracking><Tracking event=\"thirdQuartile\">http://www.steelsky.co.nz/vastEvent?thirdQuartile=1</Tracking></TrackingEvents></Linear></Creative></Creatives></InLine></Ad></VAST>", "crid": "PPV VIDEO creative id2", "adomain": ["page.com", "page2.com"], "ext": {"avn": "advertiser", "agn": "agency", "exchangeReference": {"token": "ppv-io-token"}}}]}]}}]}, {"name": "sociomantic", "endpoint": "http://__HOST__DOMAIN__:__PORT__/rtb/sociomantic/bid", "requests": 0, "responses": [{"prebidServer": false, "conditional": true, "conditionalFunc": "function shouldRespond(request) { \n    return request.imp[0].pmp.deals[0].id === \"kickback-deal\";\n}", "statusCode": 200, "name": "Kickback Response", "response": {"id": "393", "bidid": "8284", "cur": "US", "customdata": "some custom data", "seatbid": [{"group": 0, "seat": "974522", "bid": [{"id": "bidder id1", "impid": "1", "price": "3", "dealid": "kickback-deal", "adid": "2822", "adm": "<script id='mocks-advert' data-content='Sociomantic __WIDTH__x__HEIGHT__ Price macros clear: ${AUCTION_PRICE} encrypted: ${AUCTION_PRICE:ENC} TCF Macros applies: ${GDPR} personal-data: ${GDPR_PD}' data-width=__WIDTH__ data-height=__HEIGHT__ data-css-link='https://__HOST__/ad.css' src='https://__HOST__/advert.js'></script>", "crid": "creative id1", "w": "__WIDTH__", "h": "__HEIGHT__", "adomain": ["domain1.com", "domain2.com"], "ext": {"avn": "sociomanticAvn", "agn": "sociomanticAgn"}}]}]}}, {"prebidServer": false, "conditional": true, "conditionalFunc": "function shouldRespond(request) { \n return request?.site?.domain === 't-online.de' || request?.ext?.labels?.some(label => label === 'adpod'); \n}", "statusCode": 200, "name": "AdPod Response", "response": {"id": "adpod-sociomantic(990)", "bidid": "8284", "cur": "EUR", "customdata": "some custom data", "seatbid": [{"group": 0, "bid": [{"id": "Sociomantic AdPod bid", "impid": "1", "price": "5.96", "adid": "2822", "adm": "<VAST version=\"2.0\"><Ad id=\"12345\"><InLine><AdSystem>Adscale</AdSystem><AdTitle>Mountebank preroll</AdTitle><Impression>http://static.steelsky.co.nz</Impression><Creatives><Creative><Linear><Duration>00:00:10</Duration><VideoClicks><ClickThrough>http://www.advert3.com</ClickThrough></VideoClicks><MediaFiles><MediaFile height=\"608\" width=\"1080\" type=\"video/mp4\" delivery=\"progressive\">https://s3-ap-southeast-2.amazonaws.com/adscale-test-ads/advert2.mp4</MediaFile></MediaFiles><TrackingEvents><Tracking event=\"start\">http://www.steelsky.co.nz/vastEvent?start=1</Tracking><Tracking event=\"complete\">http://www.steelsky.co.nz/vastEvent?complete=1</Tracking><Tracking event=\"firstQuartile\">http://www.steelsky.co.nz/vastEvent?firstQuartile=1</Tracking><Tracking event=\"midpoint\">http://www.steelsky.co.nz/vastEvent?midpoint=1</Tracking><Tracking event=\"thirdQuartile\">http://www.steelsky.co.nz/vastEvent?thirdQuartile=1</Tracking></TrackingEvents></Linear></Creative></Creatives></InLine></Ad></VAST>", "crid": "Sociomantic AdPod crid", "adomain": ["obi.de"], "ext": {"avn": "<PERSON><PERSON>", "agn": "SociomanticObiAgn", "at": 1}}]}]}}, {"prebidServer": false, "conditional": true, "conditionalFunc": "function shouldRespond(request) { \n    return request.imp[0].video !== undefined;\n}", "statusCode": 200, "name": "Video Response", "response": {"id": "393", "bidid": "8284", "cur": "US", "customdata": "some custom data", "seatbid": [{"group": 0, "bid": [{"id": "bidder id1", "impid": "1", "price": "10.57", "adid": "2822", "adm": "<VAST version=\"2.0\"><Ad id=\"12345\"><InLine><AdSystem>Adscale</AdSystem><AdTitle>Mountebank preroll</AdTitle><Impression>http://static.steelsky.co.nz</Impression><Creatives><Creative><Linear><Duration>00:00:30</Duration><VideoClicks><ClickThrough>http://www.advert3.com</ClickThrough></VideoClicks><MediaFiles><MediaFile height=\"608\" width=\"1080\" type=\"video/mp4\" delivery=\"progressive\">https://s3-ap-southeast-2.amazonaws.com/adscale-test-ads/advert1.mp4</MediaFile></MediaFiles><TrackingEvents><Tracking event=\"start\">http://www.steelsky.co.nz/vastEvent?start=1</Tracking><Tracking event=\"complete\">http://www.steelsky.co.nz/vastEvent?complete=1</Tracking><Tracking event=\"firstQuartile\">http://www.steelsky.co.nz/vastEvent?firstQuartile=1</Tracking><Tracking event=\"midpoint\">http://www.steelsky.co.nz/vastEvent?midpoint=1</Tracking><Tracking event=\"thirdQuartile\">http://www.steelsky.co.nz/vastEvent?thirdQuartile=1</Tracking></TrackingEvents></Linear></Creative></Creatives></InLine></Ad></VAST>", "crid": "creative id1", "w": "__WIDTH__", "h": "__HEIGHT__", "adomain": ["domain1.com", "domain2.com"], "ext": {"avn": "sociomanticAvn", "agn": "sociomanticAgn"}}]}]}}, {"prebidServer": false, "conditional": true, "conditionalFunc": "function shouldRespond(request) { \n    return request.imp[0].pmp.deals[0].id === '5apaz0q7sc'; \n}", "statusCode": 200, "name": "Programmatic Guaranteed Response", "response": {"id": "393", "bidid": "8284", "cur": "US", "customdata": "some custom data", "seatbid": [{"group": 0, "seat": "396112", "bid": [{"id": "bidder id1", "dealid": "5apaz0q7sc", "impid": "1", "price": "4.57", "adid": "2822", "adm": "<script id='mocks-advert' data-content='Sociomantic __WIDTH__x__HEIGHT__ Price macros clear: ${AUCTION_PRICE} encrypted: ${AUCTION_PRICE:ENC} TCF Macros applies: ${GDPR} personal-data: ${GDPR_PD}' data-width=__WIDTH__ data-height=__HEIGHT__ data-css-link='https://__HOST__/ad.css' src='https://__HOST__/advert.js'></script>", "crid": "creative id1", "w": "__WIDTH__", "h": "__HEIGHT__", "adomain": ["domain1.com", "domain2.com"], "ext": {"avn": "sociomanticAvn", "agn": "sociomanticAgn"}}]}]}}, {"prebidServer": false, "conditional": false, "conditionalFunc": "function shouldRespond(request) { \n    return request.imp[0].video !== undefined;\n}", "statusCode": 200, "name": "HTML Response", "response": {"id": "393", "bidid": "8284", "cur": "US", "customdata": "some custom data", "seatbid": [{"group": 0, "bid": [{"id": "bidder id1", "impid": "1", "price": "4.57", "adid": "2822", "adm": "<script id='mocks-advert' data-content='Sociomantic __WIDTH__x__HEIGHT__ Price macros clear: ${AUCTION_PRICE} encrypted: ${AUCTION_PRICE:ENC} TCF Macros applies: ${GDPR} personal-data: ${GDPR_PD}' data-width=__WIDTH__ data-height=__HEIGHT__ data-css-link='https://__HOST__/ad.css' src='https://__HOST__/advert.js'></script>", "crid": "creative id1", "w": "__WIDTH__", "h": "__HEIGHT__", "adomain": ["domain1.com", "domain2.com"], "ext": {"avn": "sociomanticAvn", "agn": "sociomanticAgn"}}]}]}}]}, {"name": "mockrtb", "endpoint": "http://__HOST__DOMAIN__:__PORT__/rtb/mockrtb/bid", "requests": 0, "responses": [{"prebidServer": false, "conditional": true, "conditionalFunc": "function shouldRespond(request) { \n    return request.imp[0].tagid == \"39030\"; \n}", "statusCode": 200, "name": "MailAd Response", "response": {"id": "393", "bidid": "8284", "cur": "US", "customdata": "some custom data", "seatbid": [{"group": 0, "bid": [{"id": "bidder id1", "impid": "1", "price": "3.57", "adid": "2822", "adm": "{\"type\":\"News\",\"images\":{\"logo\":{\"url\":\"https://cdn.tmobile.com/content/dam/t-mobile/ntm/branding/logos/corporate/tmo-logo-v3.svg\"}},\"text\":{\"title\":\"Test Title\",\"content\":\"This is a test\",\"content2\":\"Don't be tricked\"},\"appclickurl\":\"\",\"clickurl\":\"youvemadeittotheclickplace.com\",\"bgcolour\":\"\",\"trackers\":[{\"type\":\"tracker\",\"url\":\"tracker69.com\"},{\"type\":\"tracker\",\"url\":\"tracker420.com\"}]}", "crid": "creative id1", "w": "__WIDTH__", "h": "__HEIGHT__", "adomain": ["domain1.com", "domain2.com"], "ext": {"avn": "mockrtbAvn", "agn": "mockrtbAgn"}}]}]}}, {"prebidServer": false, "conditional": true, "conditionalFunc": "function shouldRespond(request) { \n return request?.site?.domain === 't-online.de' || request?.ext?.labels?.some(label => label === 'adpod'); \n}", "statusCode": 200, "name": "AdPod Response", "response": {"id": "video2-mockrtb(991)", "bidid": "8284", "cur": "US", "customdata": "some custom data", "seatbid": [{"group": 0, "bid": [{"id": "bidder id1", "impid": "1", "price": "3.57", "adid": "2822", "adm": "<VAST version=\"2.0\"><Ad id=\"12345\"><InLine><AdSystem>Adscale</AdSystem><AdTitle>Mountebank preroll</AdTitle><Impression>http://static.steelsky.co.nz</Impression><Creatives><Creative><Linear><Duration>00:00:10</Duration><VideoClicks><ClickThrough>http://www.advert3.com</ClickThrough></VideoClicks><MediaFiles><MediaFile height=\"608\" width=\"1080\" type=\"video/mp4\" delivery=\"progressive\">https://s3-ap-southeast-2.amazonaws.com/adscale-test-ads/advert2.mp4</MediaFile></MediaFiles><TrackingEvents><Tracking event=\"start\">http://www.steelsky.co.nz/vastEvent?start=1</Tracking><Tracking event=\"complete\">http://www.steelsky.co.nz/vastEvent?complete=1</Tracking><Tracking event=\"firstQuartile\">http://www.steelsky.co.nz/vastEvent?firstQuartile=1</Tracking><Tracking event=\"midpoint\">http://www.steelsky.co.nz/vastEvent?midpoint=1</Tracking><Tracking event=\"thirdQuartile\">http://www.steelsky.co.nz/vastEvent?thirdQuartile=1</Tracking></TrackingEvents></Linear></Creative></Creatives></InLine></Ad></VAST>", "crid": "Mockrtb Video crid1", "adomain": ["domain1.com", "domain2.com"], "ext": {"avn": "mockrtbAvn", "agn": "mockrtbAgn", "at": 1}}]}]}}, {"prebidServer": false, "conditional": true, "conditionalFunc": "function shouldRespond(request) { \n return request.imp[0].ext?.totalaud > 1; \n}", "statusCode": 200, "name": "LinearTV Response", "response": {"id": "393", "bidid": "8284", "cur": "US", "customdata": "some custom data", "seatbid": [{"group": 0, "bid": [{"id": "bidder id1", "impid": "1", "price": "15.57", "adid": "2822", "adm": "<VAST version=\"2.0\"><Ad id=\"12345\"><InLine><AdSystem>Adscale</AdSystem><AdTitle>Mountebank preroll - Linear TV</AdTitle><Impression>https://static.steelsky.co.nz?audience=${TOTAL_IMP}</Impression><Creatives><Creative><Linear><Duration>00:00:10</Duration><VideoClicks><ClickThrough>http://www.advert3.com</ClickThrough></VideoClicks><MediaFiles><MediaFile height=\"608\" width=\"1080\" type=\"video/mp4\" delivery=\"progressive\">https://s3-ap-southeast-2.amazonaws.com/adscale-test-ads/advert2.mp4</MediaFile></MediaFiles><TrackingEvents><Tracking event=\"start\">http://www.steelsky.co.nz/vastEvent?start=1</Tracking><Tracking event=\"complete\">http://www.steelsky.co.nz/vastEvent?complete=1</Tracking><Tracking event=\"firstQuartile\">http://www.steelsky.co.nz/vastEvent?firstQuartile=1</Tracking><Tracking event=\"midpoint\">http://www.steelsky.co.nz/vastEvent?midpoint=1</Tracking><Tracking event=\"thirdQuartile\">http://www.steelsky.co.nz/vastEvent?thirdQuartile=1</Tracking></TrackingEvents></Linear></Creative></Creatives></InLine></Ad></VAST>", "crid": "Mockrtb_Video_LinearTv2", "adomain": ["domain1.com", "domain2.com"], "ext": {"avn": "mockrtbAvn", "agn": "mockrtbAgn"}}]}]}}, {"prebidServer": false, "conditional": false, "conditionalFunc": "function shouldRespond(request) { \n    return request.imp[0].video !== undefined; \n}", "statusCode": 200, "name": "Default Response", "response": {"id": "393", "bidid": "8284", "cur": "US", "customdata": "some custom data", "seatbid": [{"group": 0, "bid": [{"id": "bidder id1", "impid": "1", "price": "3.57", "adid": "2822", "adm": "<script id='mocks-advert' data-content='Mockrtb __WIDTH__x__HEIGHT__ Price macros clear: ${AUCTION_PRICE} encrypted: ${AUCTION_PRICE:ENC} TCF Macros applies: ${GDPR} personal-data: ${GDPR_PD}' data-width=__WIDTH__ data-height=__HEIGHT__ data-css-link='https://__HOST__/ad.css' src='https://__HOST__/advert.js'></script>", "crid": "creative id1", "w": "__WIDTH__", "h": "__HEIGHT__", "adomain": ["domain1.com", "domain2.com"], "ext": {"avn": "mockrtbAvn", "agn": "mockrtbAgn"}}]}]}}]}, {"name": "appNexus", "endpoint": "http://__HOST__DOMAIN__:__PORT__/rtb/appNexus/bid", "requests": 0, "responses": [{"prebidServer": false, "conditional": false, "conditionalFunc": "function shouldRespond(request) { \n   return request.imp[0].video !== undefined; \n}", "statusCode": 200, "name": "Default Response", "response": {"id": "393", "bidid": "8284", "cur": "US", "customdata": "some custom data", "seatbid": [{"group": 0, "bid": [{"id": "bidder id1", "impid": "1", "price": "4.57", "adid": "2822", "adm": "<script id='mocks-advert' data-content='Appnexus __WIDTH__x__HEIGHT__ Price macros clear: ${AUCTION_PRICE} encrypted: ${AUCTION_PRICE:ENC} TCF Macros applies: ${GDPR} personal-data: ${GDPR_PD}' data-width=__WIDTH__ data-height=__HEIGHT__ data-css-link='https://__HOST__/ad.css' src='https://__HOST__/advert.js'></script>", "crid": "creative id1", "w": "__WIDTH__", "h": "__HEIGHT__", "adomain": ["domain1.com", "domain2.com"], "ext": {"avn": "appNexusAvn", "agn": "appNexusAgn"}}]}]}}]}, {"name": "adform", "endpoint": "http://__HOST__DOMAIN__:__PORT__/rtb/adform/bid", "requests": 0, "responses": [{"prebidServer": false, "conditional": true, "conditionalFunc": "function shouldRespond(request) { \n    return request.imp[0].video !== undefined && request.site.domain !== 't-online.de'; \n}", "statusCode": 200, "name": "Default Response", "response": {"id": "393", "bidid": "8284", "cur": "US", "customdata": "some custom data", "seatbid": [{"group": 0, "seat": "12345", "bid": [{"id": "bidder id1", "impid": "1", "price": "12.57", "adid": "2822", "adm": "<VAST version=\"2.0\"><Ad id=\"12345\"><InLine><AdSystem>Adscale</AdSystem><AdTitle>Mountebank preroll</AdTitle><Impression>http://static.steelsky.co.nz</Impression><Creatives><Creative><Linear><Duration>00:00:30</Duration><VideoClicks><ClickThrough>http://www.advert3.com</ClickThrough></VideoClicks><MediaFiles><MediaFile height=\"608\" width=\"1080\" type=\"video/mp4\" delivery=\"progressive\">https://s3-ap-southeast-2.amazonaws.com/adscale-test-ads/advert1.mp4</MediaFile></MediaFiles><TrackingEvents><Tracking event=\"start\">http://www.steelsky.co.nz/vastEvent?start=1</Tracking><Tracking event=\"complete\">http://www.steelsky.co.nz/vastEvent?complete=1</Tracking><Tracking event=\"firstQuartile\">http://www.steelsky.co.nz/vastEvent?firstQuartile=1</Tracking><Tracking event=\"midpoint\">http://www.steelsky.co.nz/vastEvent?midpoint=1</Tracking><Tracking event=\"thirdQuartile\">http://www.steelsky.co.nz/vastEvent?thirdQuartile=1</Tracking></TrackingEvents></Linear></Creative></Creatives></InLine></Ad></VAST>", "crid": "VIDEO creative id1", "adomain": ["domain1.com", "domain2.com"], "ext": {"avn": "VIDEOavn", "agn": "VIDEOagn", "at": 1, "priority": 2, "maxprice": 12.57}}]}]}}, {"prebidServer": false, "conditional": true, "conditionalFunc": "function shouldRespond(request) { \n return request?.site?.domain === 't-online.de' || request?.ext?.labels?.some(label => label === 'adpod'); \n}", "statusCode": 200, "name": "AdPod Response", "response": {"id": "video1-adform(992)", "bidid": "8284", "cur": "US", "customdata": "some custom data", "seatbid": [{"group": 0, "seat": "12345", "bid": [{"id": "bidder id1", "impid": "1", "price": "12.99", "adid": "2822", "adm": "<VAST version=\"2.0\"><Ad id=\"12345\"><InLine><AdSystem>Adscale</AdSystem><AdTitle>Mountebank preroll</AdTitle><Impression>http://static.steelsky.co.nz</Impression><Creatives><Creative><Linear><Duration>00:00:10</Duration><VideoClicks><ClickThrough>http://www.advert3.com</ClickThrough></VideoClicks><MediaFiles><MediaFile height=\"608\" width=\"1080\" type=\"video/mp4\" delivery=\"progressive\">https://s3-ap-southeast-2.amazonaws.com/adscale-test-ads/advert1.mp4</MediaFile></MediaFiles><TrackingEvents><Tracking event=\"start\">http://www.steelsky.co.nz/vastEvent?start=1</Tracking><Tracking event=\"complete\">http://www.steelsky.co.nz/vastEvent?complete=1</Tracking><Tracking event=\"firstQuartile\">http://www.steelsky.co.nz/vastEvent?firstQuartile=1</Tracking><Tracking event=\"midpoint\">http://www.steelsky.co.nz/vastEvent?midpoint=1</Tracking><Tracking event=\"thirdQuartile\">http://www.steelsky.co.nz/vastEvent?thirdQuartile=1</Tracking></TrackingEvents></Linear></Creative></Creatives></InLine></Ad></VAST>", "crid": "Goldbach Media crid1", "adomain": ["goldbachmedia.at"], "ext": {"avn": "Goldbach Media", "agn": "Goldbach Media AT", "at": 1}}]}]}}]}, {"name": "thetradedesk", "endpoint": "http://__HOST__DOMAIN__:__PORT__/rtb/thetradedesk/bid", "requests": 0, "responses": [{"prebidServer": false, "conditional": false, "conditionalFunc": "function shouldRespond(request) { \n    return request.imp[0].video !== undefined; \n}", "statusCode": 200, "name": "Default Response", "response": {"id": "393", "bidid": "8284", "cur": "EUR", "customdata": "some custom data", "seatbid": [{"group": 0, "seat": "12345", "bid": [{"id": "bidder id1", "impid": "1", "price": "12.57", "adid": "2822", "adm": {"native": {"link": {"url": "http://www.tchibo.de/von-fleissigen-bienchen-empfohlen-t400083487.html"}, "imptrackers": ["http://www.this.is.an.imp.tracker.com/trackie", "https://www.so.is.this.co.uk/peekaboo?pi=314"], "assets": [{"id": 1, "required": 1, "title": {"text": "Watch this awesome thing"}}, {"id": 2, "required": 1, "img": {"url": "https://adimg.uimserv.net/TCHIBO/KW_41/2017/NF_KW4117_HVK_Brit-Chic-Ladies_DE_300x250_Overlay.jpg"}}, {"id": 3, "required": 1, "data": {"value": "My Brand"}}, {"id": 4, "required": 1, "data": {"value": "Try it out and you will find out."}}]}}, "crid": "VIDEO creative id1", "adomain": ["domain1.com", "domain2.com"], "ext": {"avn": "VIDEOavn", "agn": "VIDEOagn"}}]}]}}]}, {"name": "bidswitch", "endpoint": "http://__HOST__DOMAIN__:__PORT__/rtb/bidswitch/bid", "requests": 0, "responses": [{"prebidServer": false, "conditional": true, "conditionalFunc": "function shouldRespond(request) { \n    return request.badv.includes('ppv-flexible-buyer'); \n}", "statusCode": 200, "name": "Flexible Buyer Response", "response": {"id": "393", "bidid": "8284", "cur": "US", "customdata": "some custom data", "seatbid": [{"group": 0, "seat": "904865", "bid": [{"adm": "<?xml version='1.0' encoding='UTF-8'?> <VAST version='2.0'> <Ad id='1'> <InLine> <AdSystem>Christian_Test</AdSystem> <AdTitle>B2-9-16_mds_15Sek_regiohelden.mp4</AdTitle> <Impression>http://mocks.tools.adscale.de:/rtb/ppv-nz/bid</Impression> <Creatives> <Creative id='Regiohelden_Test_Creative'> <Linear> <Duration>00:00:15</Duration> <TrackingEvents/> <MediaFiles> <MediaFile type='video/mp4' width='1080' height='1920' maintainAspectRatio='true' scalable='false'><![CDATA[https://dgg3rnz8nudgw.cloudfront.net/c8a1efca-4ccf-4d51-a3aa-98a81a740477/creative.mp4]]></MediaFile> </MediaFiles> </Linear> </Creative> </Creatives> </InLine> </Ad> </VAST>", "adomain": ["voyager-test.com"], "crid": "flexible-creative-1", "ext": {"agn": "voyager-test-agency-2", "avn": "voyager-test-advertiser"}, "id": "1", "dealid": "chdzvn2lcj", "impid": "1", "price": 10}]}]}}, {"prebidServer": false, "conditional": true, "conditionalFunc": "function shouldRespond(request) { \n return request.imp[0].audio.mimes.includes('audio/mp3'); \n}", "statusCode": 200, "name": "Audio Response", "response": {"id": "audio-bidswitch(999)", "bidid": "8284", "cur": "EUR", "customdata": "some custom data", "seatbid": [{"group": 0, "bid": [{"id": "Bidswitch Audio bid", "impid": "1", "price": "13.59", "adid": "2822", "adm": "<VAST version=\"2.0\"><Ad id=\"12345\"><InLine><AdSystem>Adscale</AdSystem><AdTitle>Bidswitch Audio preroll</AdTitle><Impression>http://static.steelsky.co.nz</Impression><Creatives><Creative><Linear><Duration>00:00:10</Duration><VideoClicks><ClickThrough>http://www.advert3.com</ClickThrough></VideoClicks><MediaFiles><MediaFile height=\"608\" width=\"1080\" type=\"video/mp4\" delivery=\"progressive\">https://s3-ap-southeast-2.amazonaws.com/adscale-test-ads/advert2.mp4</MediaFile></MediaFiles><TrackingEvents><Tracking event=\"start\">http://www.steelsky.co.nz/vastEvent?start=1</Tracking><Tracking event=\"complete\">http://www.steelsky.co.nz/vastEvent?complete=1</Tracking><Tracking event=\"firstQuartile\">http://www.steelsky.co.nz/vastEvent?firstQuartile=1</Tracking><Tracking event=\"midpoint\">http://www.steelsky.co.nz/vastEvent?midpoint=1</Tracking><Tracking event=\"thirdQuartile\">http://www.steelsky.co.nz/vastEvent?thirdQuartile=1</Tracking></TrackingEvents></Linear></Creative></Creatives></InLine></Ad></VAST>", "crid": "Bidswitch Video Adpod crid", "adomain": ["toyota.de"], "ext": {"avn": "Toyota", "agn": "BidswitchToyotaAgn", "at": 1}}]}]}}, {"prebidServer": false, "conditional": true, "conditionalFunc": "function shouldRespond(request) { \n return request?.site?.domain === 't-online.de' || request?.ext?.labels?.some(label => label === 'adpod'); \n}", "statusCode": 200, "name": "AdPod Response", "response": {"id": "adpod-bidswitch(999)", "bidid": "8284", "cur": "EUR", "customdata": "some custom data", "seatbid": [{"group": 0, "bid": [{"id": "Bidswitch AdPod bid", "impid": "1", "price": "4.96", "adid": "2822", "adm": "<VAST version=\"2.0\"><Ad id=\"12345\"><InLine><AdSystem>Adscale</AdSystem><AdTitle>Mountebank preroll</AdTitle><Impression>http://static.steelsky.co.nz</Impression><Creatives><Creative><Linear><Duration>00:00:10</Duration><VideoClicks><ClickThrough>http://www.advert3.com</ClickThrough></VideoClicks><MediaFiles><MediaFile height=\"608\" width=\"1080\" type=\"video/mp4\" delivery=\"progressive\">https://s3-ap-southeast-2.amazonaws.com/adscale-test-ads/advert2.mp4</MediaFile></MediaFiles><TrackingEvents><Tracking event=\"start\">http://www.steelsky.co.nz/vastEvent?start=1</Tracking><Tracking event=\"complete\">http://www.steelsky.co.nz/vastEvent?complete=1</Tracking><Tracking event=\"firstQuartile\">http://www.steelsky.co.nz/vastEvent?firstQuartile=1</Tracking><Tracking event=\"midpoint\">http://www.steelsky.co.nz/vastEvent?midpoint=1</Tracking><Tracking event=\"thirdQuartile\">http://www.steelsky.co.nz/vastEvent?thirdQuartile=1</Tracking></TrackingEvents></Linear></Creative></Creatives></InLine></Ad></VAST>", "crid": "Bidswitch Video Adpod crid", "adomain": ["toyota.de"], "ext": {"avn": "Toyota", "agn": "BidswitchToyotaAgn", "at": 1}}]}]}}, {"prebidServer": false, "conditional": true, "conditionalFunc": "function shouldRespond(request) {\n    const deals = request.imp[0].pmp.deals;\n    return deals.some(deal => deal.id === 'ppv-test-deal');\n}", "statusCode": 200, "name": "ppv-test-deal Response", "response": {"id": "393", "bidid": "8284", "cur": "US", "customdata": "some custom data", "seatbid": [{"group": 0, "seat": "904865", "bid": [{"id": "ppv bidder id1", "impid": "1", "dealid": "ppv-test-deal", "price": "12.57", "adid": "2822", "adm": "<VAST version=\"2.0\"><Ad id=\"12345\"><InLine><AdSystem>Adscale</AdSystem><AdTitle>Mountebank preroll</AdTitle><Impression>http://static.steelsky.co.nz?cost=${AUCTION_PRICE}</Impression><Creatives><Creative><Linear><Duration>00:00:30</Duration><VideoClicks><ClickThrough>http://www.advert3.com</ClickThrough></VideoClicks><MediaFiles><MediaFile height=\"608\" width=\"1080\" type=\"video/mp4\" delivery=\"progressive\">https://s3-ap-southeast-2.amazonaws.com/adscale-test-ads/advert1.mp4</MediaFile></MediaFiles><TrackingEvents><Tracking event=\"start\">http://www.steelsky.co.nz/vastEvent?start=1</Tracking><Tracking event=\"complete\">http://www.steelsky.co.nz/vastEvent?complete=1</Tracking><Tracking event=\"firstQuartile\">http://www.steelsky.co.nz/vastEvent?firstQuartile=1</Tracking><Tracking event=\"midpoint\">http://www.steelsky.co.nz/vastEvent?midpoint=1</Tracking><Tracking event=\"thirdQuartile\">http://www.steelsky.co.nz/vastEvent?thirdQuartile=1</Tracking></TrackingEvents></Linear></Creative></Creatives></InLine></Ad></VAST>", "crid": "PPV VIDEO creative id1", "adomain": ["domain1.com", "domain2.com"], "ext": {"avn": "PPV VIDEO avn", "agn": "PPV VIDEO agn"}}]}]}}, {"prebidServer": false, "conditional": true, "conditionalFunc": "function shouldRespond(request) {\n    const deals = request.imp[0].pmp.deals;\n    return deals.some(deal => deal.id === 'ppv-test-deal-2');\n}", "statusCode": 200, "name": "ppv-test-deal-2 Response", "response": {"id": "393", "bidid": "8284", "cur": "US", "customdata": "some custom data", "seatbid": [{"group": 0, "seat": "904865", "bid": [{"id": "guaranteed ppv bidder", "impid": "1", "dealid": "ppv-test-deal-2", "price": "12.57", "adid": "2822", "adm": "<VAST version=\"2.0\"><Ad id=\"12345\"><InLine><AdSystem>Adscale</AdSystem><AdTitle>Mountebank preroll</AdTitle><Impression>http://static.steelsky.co.nz?cost=${AUCTION_PRICE}</Impression><Creatives><Creative><Linear><Duration>00:00:30</Duration><VideoClicks><ClickThrough>http://www.advert3.com</ClickThrough></VideoClicks><MediaFiles><MediaFile height=\"608\" width=\"1080\" type=\"video/mp4\" delivery=\"progressive\">https://s3-ap-southeast-2.amazonaws.com/adscale-test-ads/advert1.mp4</MediaFile></MediaFiles><TrackingEvents><Tracking event=\"start\">http://www.steelsky.co.nz/vastEvent?start=1</Tracking><Tracking event=\"complete\">http://www.steelsky.co.nz/vastEvent?complete=1</Tracking><Tracking event=\"firstQuartile\">http://www.steelsky.co.nz/vastEvent?firstQuartile=1</Tracking><Tracking event=\"midpoint\">http://www.steelsky.co.nz/vastEvent?midpoint=1</Tracking><Tracking event=\"thirdQuartile\">http://www.steelsky.co.nz/vastEvent?thirdQuartile=1</Tracking></TrackingEvents></Linear></Creative></Creatives></InLine></Ad></VAST>", "crid": "PPV VIDEO creative id1", "adomain": ["domain1.com", "domain2.com"], "ext": {"avn": "PPV VIDEO avn", "agn": "PPV VIDEO agn"}}]}]}}]}, {"name": "default", "endpoint": "http://__HOST__DOMAIN__:__PORT__/rtb/default/bid", "requests": 0, "responses": [{"prebidServer": false, "conditional": false, "conditionalFunc": "function shouldRespond(request) { return true; }", "statusCode": 200, "name": "Default Response", "response": {"id": "393", "bidid": "8284", "cur": "US", "customdata": "some custom data", "seatbid": [{"group": 0, "bid": [{"id": "bidder id1", "impid": "1", "price": "5.57", "adid": "2822", "adm": "<script id='mocks-advert' data-content='Buy Me __WIDTH__x__HEIGHT__ Price macros clear: ${AUCTION_PRICE} encrypted: ${AUCTION_PRICE:ENC} TCF Macros applies: ${GDPR} personal-data: ${GDPR_PD}' data-width=__WIDTH__ data-height=__HEIGHT__ data-css-link='https://__HOST__/ad.css' src='https://__HOST__/advert.js'></script>", "crid": "creative id1", "w": "__WIDTH__", "h": "__HEIGHT__", "adomain": ["domain1.com", "domain2.com"], "ext": {"avn": "adv1488848261", "agn": "agn881772483"}}]}]}}]}]}