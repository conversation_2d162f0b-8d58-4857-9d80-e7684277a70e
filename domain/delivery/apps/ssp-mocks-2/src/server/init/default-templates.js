import {findAll, insert} from "../datastore/mongo";
import {Collections} from "../constants/collections";

let defaultResponse = {
    "template": {
        "name": "example", "response": {
            "id": "393", "bidid": "8284", "cur": "US", "customdata": "some custom data", "seatbid": [{
                "group": 0, "bid": [{
                    "id": "bidder id1",
                    "impid": "1",
                    "price": "5",
                    "adid": "2822",
                    "adm": "<script id='mocks-advert' data-content='Buy Me __WIDTH__x__HEIGHT__' data-width=__WIDTH__ data-height=__HEIGHT__ data-css-link='//__HOST__/ad/ad.css' src='//__HOST__/ad/advert.js'/>",
                    "crid": "alpha crid",
                    "w": "__WIDTH__",
                    "h": "__HEIGHT__",
                    "adomain": ["domain1.com", "domain2.com"],
                    "ext": {
                        "avn": "alphaAvn", "agn": "alphaAgn"
                    }
                }]
            }]

        }
    }
};
let kanye = {
    "template": {
        "name": "kanyetest", "response": {
            "id": "393", "bidid": "8284", "customdata": "some custom data", "seatbid": [{
                "seat": "752754", "group": 0, "bid": [{
                    "id": "bidder id1",
                    "impid": "1",
                    "price": "0.06",
                    "adid": "2822",
                    "adm": "<img src='https://ccii.org.nz/app/uploads/2013/01/Cornwall-Park-800x250.jpg' height='__HEIGHT__' width='__WIDTH__' />",
                    "crid": "stroeerlabsnz",
                    "w": "800",
                    "h": "250",
                    "adomain": ["jobs.stroeerlabs.com"],
                    "ext": {
                        "avn": "stroeernz-test", "agn": "stroeernz-test-agency"
                    }
                }]
            }]
        }
    }
};

findAll(Collections.TEMPLATES).then((templates) => {
    if (templates && templates.length === 0) {
        insert(Collections.TEMPLATES, defaultResponse);
        insert(Collections.TEMPLATES, kanye);
    }
});
