import {findAll, insert} from "../datastore/mongo";
import {Collections} from "../constants/collections";

const associationForDisplaySelfhostedAd = {
  "adId": "7317996b-ee31-491b-9f4e-78ec3c530e07",
  "campaignId": "815e6e91-05bb-4e61-b482-27b9f59e47c7",
  "active": true,
  "adAssociationId": "431d50b2-888e-4967-9747-000000000000",
  "defaultDomain": "https://default-domain.url",
  "lastModification": 1000000,
  "scheduleDelivery": null,
  "targetedAdSlotFormats": [
    {
      "format": "612x408"
    }
  ],
  "url": "https://clicl-url.com",
  "apiSyncProcessId": "5d849886-0325-4ad4-b9d9-5e4add5f1ea3",
  "bidderId": "431d50b2-888e-4967-9747-76f6dec9dd7f",
  "lastSync": 1000001,
};

const associationForDisplay3rdPartyAd = {
  "adId": "82e13817-0b8c-415d-9421-9ed22b3bb80c",
  "campaignId": "815e6e91-05bb-4e61-b482-27b9f59e47c7",
  "active": true,
  "adAssociationId": "431d50b2-888e-4967-9747-000000000001",
  "defaultDomain": "https://default-domain.url",
  "lastModification": 1000002,
  "scheduleDelivery": null,
  "targetedAdSlotFormats": [
    {
      "format": "612x408"
    }
  ],
  "url": "https://clicl-url.com",
  "apiSyncProcessId": "5d849886-0325-4ad4-b9d9-5e4add5f1ea3",
  "bidderId": "431d50b2-888e-4967-9747-76f6dec9dd7f",
  "lastSync": 1740734564842,
};

const associationForVideoSelfhostedAd = {
  "adId": "e0295023-a687-426f-b442-f4ca1a24c74f",
  "campaignId": "815e6e91-05bb-4e61-b482-27b9f59e47c7",
  "active": true,
  "adAssociationId": "431d50b2-888e-4967-9747-000000000002",
  "defaultDomain": "https://default-domain.url",
  "lastModification": 1000003,
  "scheduleDelivery": null,
  "targetedAdSlotFormats": [
    {
      "format": "612x408"
    }
  ],
  "url": "https://clicl-url.com",
  "apiSyncProcessId": "5d849886-0325-4ad4-b9d9-5e4add5f1ea3",
  "bidderId": "431d50b2-888e-4967-9747-76f6dec9dd7f",
  "lastSync": 1740734564842,
};

const associationForOutOfHomeVideoAd = {
  "adId": "4bc91c97-8c1f-4411-8f93-de46a17e9a60",
  "campaignId": "815e6e91-05bb-4e61-b482-27b9f59e47c7",
  "active": true,
  "adAssociationId": "431d50b2-888e-4967-9747-000000000003",
  "defaultDomain": "https://default-domain.url",
  "lastModification": 1000004,
  "scheduleDelivery": null,
  "targetedAdSlotFormats": [
    {
      "format": "612x408"
    }
  ],
  "url": "https://clicl-url.com",
  "apiSyncProcessId": "5d849886-0325-4ad4-b9d9-5e4add5f1ea3",
  "bidderId": "431d50b2-888e-4967-9747-76f6dec9dd7f",
  "lastSync": 1740734564842,
};

const associationForCoreBidderCreativeId1 = {
  "adId": "core-bidder-creative-id-1",
  "campaignId": "core-bidder-campaign-id-1",
  "active": true,
  "adAssociationId": "7b80345c-d7d9-4b44-94a9-a9d0039b5ed7",
  "defaultDomain": "https://default-domain.url",
  "lastModification": 1000004,
  "scheduleDelivery": null,
  "targetVideoAdSlotTypes": [
    {
      "videoAdSlotType": "in-stream",
      "inStreamPosition": "pre-roll"
    }
  ],
  "url": null,
  "weight": 1,
  "apiSyncProcessId": "5d849886-0325-4ad4-b9d9-5e4add5f1ea3",
  "bidderId": "431d50b2-888e-4967-9747-76f6dec9dd7f",
  "lastSync": 1740734564842,
};

const associationForMobileExternalStickyFooter = {
  "adId": "7a233231-0ec8-4e7d-b47c-76b16b3bbe6e",
  "campaignId": "815e6e91-05bb-4e61-b482-27b9f59e47c7",
  "active": true,
  "adAssociationId": "431d50b2-888e-4967-9747-000000000004",
  "defaultDomain": "https://default-domain.url",
  "lastModification": 1000005,
  "scheduleDelivery": null,
  "targetedAdSlotFormats": [
    {
      "format": "612x408"
    }
  ],
  "url": "https://clicl-url.com",
  "apiSyncProcessId": "5d849886-0325-4ad4-b9d9-5e4add5f1ea3",
  "bidderId": "431d50b2-888e-4967-9747-76f6dec9dd7f",
  "lastSync": 1740734564842,
};

const associationForDisplayFireplaceAd = {
  "adId": "ffdefe5c-0263-4383-9a1d-2381f234c77b",
  "campaignId": "815e6e91-05bb-4e61-b482-27b9f59e47c7",
  "active": true,
  "adAssociationId": "431d50b2-888e-4967-9747-000000000005",
  "defaultDomain": "https://default-domain.url",
  "lastModification": 1000005,
  "scheduleDelivery": null,
  "targetedAdSlotFormats": [
    {
      "format": "612x408"
    }
  ],
  "url": "https://click-url.com",
  "apiSyncProcessId": "5d849886-0325-4ad4-b9d9-5e4add5f1ea3",
  "bidderId": "431d50b2-888e-4967-9747-76f6dec9dd7f",
  "lastSync": 1740734564842,
};



findAll(Collections.AD_ASSOCIATIONS).then((associations) => {
    if (associations?.length === 0) {
        insert(Collections.AD_ASSOCIATIONS,  [
            associationForMobileExternalStickyFooter,
            associationForOutOfHomeVideoAd,
            associationForCoreBidderCreativeId1,
            associationForVideoSelfhostedAd,
            associationForDisplay3rdPartyAd,
            associationForDisplaySelfhostedAd,
            associationForDisplayFireplaceAd
        ]);
    }
});
