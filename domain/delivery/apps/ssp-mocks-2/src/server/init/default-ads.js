import {findAll, insert} from "../datastore/mongo";
import {Collections} from "../constants/collections";

const displaySelfhostedAd = {
      "id": "7317996b-ee31-491b-9f4e-78ec3c530e07",
      "type": "displayAd",
      "adTitle": "Display selfhosted ad",
      "customerId": "6970a860-d586-4c4d-88f0-b7b5fe637c38",
      "businessRoleId": "cc355355-bd66-4555-adec-20b54d923054",
      "_timestamp": 500000,
      "displayAdData": {
        "adType": "standard",
        "sourceType": "selfhosted"
      },
      "assets": [
        {
          "sourceType": "mediaFileContainer",
          "mediaFileContainerId": "432eca74-5189-4276-b7ee-fbfc4e3b27f3",
          "dimensions": {
            "width": 612,
            "height": 408
          },
          "durationInSec": 10
        }
      ],
      "dimensions": {
        "width": 612,
        "height": 408
      },
      "externalSystem": {
        "dfp": {
          "id": "34"
        }
      },
      "mbrBidderId": "a1aaf16c-efe3-403a-a25c-934699d3f7fb"
};

const display3rdPartyAd = {
      "id": "82e13817-0b8c-415d-9421-9ed22b3bb80c",
      "type": "displayAd",
      "adTitle": "Display wrapper ad",
      "customerId": "6970a860-d586-4c4d-88f0-b7b5fe637c38",
      "businessRoleId": "cc355355-bd66-4555-adec-20b54d923054",
      "_timestamp": 500001,
      "externalSystem": {
        "dfp": {
          "id": "35"
        }
      },
      "displayAdData": {
        "adType": "standard",
        "sourceType": "3rdParty"
      },
      "assets": [
        {
          "sourceType": "script",
          "dimensions": {
            "width": 300,
            "height": 250
          },
          "script": {
            "content": "<script>var a = 'script content';</script>"
          }
        }
      ],
      "dimensions": {
        "id": "82e13817-0b8c-415d-9421-9ed22b3bb80c",
        "width": 300,
        "height": 250
      },
      "mbrBidderId": "9e5e16cd-b120-484d-b1cf-a1fcde516572"
};

const videoSelfhostedAd = {
    "id": "e0295023-a687-426f-b442-f4ca1a24c74f",
    "type": "video",
    "adTitle": "Video selfhosted ad",
    "customerId": "6970a860-d586-4c4d-88f0-b7b5fe637c38",
    "businessRoleId": "cc355355-bd66-4555-adec-20b54d923054",
    "_timestamp": 500002,
    "externalSystem": {
      "dfp": {
        "id": "37"
      }
    },
    "videoAdData": {
      "videoAdType": "inline",
      "durationInSec": 3.669
    },
    "assets": [
      {
        "sourceType": "mediaFileContainer",
        "dimensions": {
          "width": 854,
          "height": 480
        },
        "clickUrl": "https://click-url.com",
        "mediaFileContainerId": "45a080cd-2bf0-4fbb-8114-be723cbb763b",
        "durationInSec": 3.669
      }
    ],
    "dimensions": {
      "id": "e0295023-a687-426f-b442-f4ca1a24c74f",
      "width": 854,
      "height": 480
    },
    "durationInSec": 3.669,
    "impressionUrls": [],
    "mbrBidderId": "05393a31-1209-40bb-92c8-acb65ca34e15"
};

const outOfHomeVideoAd = {
      "id": "4bc91c97-8c1f-4411-8f93-de46a17e9a60",
      "type": "outOfHome",
      "adTitle": "Out of home ad",
      "customerId": "6970a860-d586-4c4d-88f0-b7b5fe637c38",
      "businessRoleId": "cc355355-bd66-4555-adec-20b54d923054",
      "_timestamp": 500003,
      "externalSystem": {
        "dfp": {
          "id": "39"
        }
      },
      "videoAdData": {
        "videoAdType": "inline",
        "durationInSec": 3.669
      },
      "assets": [
        {
          "sourceType": "mediaFileContainer",
          "dimensions": {
            "width": 854,
            "height": 480
          },
          "mediaFileContainerId": "1be4631d-b8b8-4779-a791-4e8400e5797f",
          "durationInSec": 3.669
        }
      ],
      "dimensions": {
        "width": 854,
        "height": 480
      },
      "durationInSec": 3.669,
      "mbrBidderId": "9a76509e-f5d3-4bb6-88d2-cc2e360e0a40",
      "motionMode": "video",
      "categories": [
        {
            "branch": ["branch-id-1"]
        },
        {
            "branch": ["branch-id-2", "branch-id-3"]
        }
      ]
};

const outOfHomeVideoAdCoreBidderCreativeId1 = {
    "id": "core-bidder-creative-id-1",
    "type": "outOfHome",
    "adTitle": "Out of home ad - core-bidder-campaign-id-1",
    "customerId": "6970a860-d586-4c4d-88f0-b7b5fe637c38",
    "businessRoleId": "cc355355-bd66-4555-adec-20b54d923054",
    "_timestamp": 500003,
    "externalSystem": {
        "dfp": {
            "id": "39"
        }
    },
    "videoAdData": {
        "videoAdType": "inline",
        "durationInSec": 24
    },
    "assets": [
        {
            "sourceType": "mediaFileContainer",
            "dimensions": {
                "width": 1920,
                "height": 1080
            },
            "mediaFileContainerId": "1be4631d-b8b8-4779-a791-4e8400e5797f",
            "durationInSec": 24
        }
    ],
    "dimensions": {
        "width": 1920,
        "height": 1080
    },
    "durationInSec": 24,
    "mbrBidderId": "9a76509e-f5d3-4bb6-88d2-cc2e360e0a40",
    "motionMode": "video",
    "categories": [
        {
            "branch": ["branch-id-1"]
        },
        {
            "branch": ["branch-id-2", "branch-id-3"]
        }
    ]
};

const mobileExternalStickyFooter =   {
    "id": "7a233231-0ec8-4e7d-b47c-76b16b3bbe6e",
    "type": "displayAd",
    "adTitle": "Mobile External Sticky Footer Ad",
    "customerId": "6970a860-d586-4c4d-88f0-b7b5fe637c38",
    "businessRoleId": "cc355355-bd66-4555-adec-20b54d923054",
    "_timestamp": 500004,
    "externalSystem": {
        "dfp": {
            "id": "320068"
        }
    },
    "displayAdData": {
        "adType": "standard",
        "sourceType": "selfhosted"
    },
    "assets": [
        {
            "sourceType": "mediaFileContainer",
            "mediaFileContainerId": "695e54d3-62b9-438e-b7c3-673627515b52",
            "dimensions": {
                "width": 320,
                "height": 52
            },
            "durationInSec": 10
        }
    ],
    "dimensions": {
        "width": 320,
        "height": 52
    },
    "trackedEvents": [
        {
            "eventType": "engagement",
            "content": "engagementTrackingCode"
        }
    ],
    "mbrBidderId": "4419429a-abb9-4127-be3b-797d467fa3dd"
};

const displayFireplaceAd = {
    "id": "ffdefe5c-0263-4383-9a1d-2381f234c77b",
    "type": "displayAd",
    "adTitle": "Fireplace",
    "customerId": "71504eaa-58bf-4bfc-83a6-7f71b2da3eca",
    "businessRoleId": "04f51a9e-ca6f-4ec3-ba49-06159e0e616d",
    "_timestamp": 500005,
    "externalSystem": {
        "dfp": {
            "id": "328797"
        }
    },
    "displayAdData": {
        "adType": "fireplace",
        "sourceType": "3rdParty",
        "fireplaceMode": "topBetweenSides",
        "fireplaceResponsivenessMode": "standard",
        "sitebarsBehavior": "standard"
    },
    "reloadPolicy": "noReload",
    "mbrBidderId": "2db8175b-9438-4dc4-99aa-593945fa39d4",
    "assets": [
        {
            "sourceType": "iframe",
            "position": "left",
            "dimensions": {
                "height": 600,
                "width": 120
            },
            "iframe": {
                "source": "second.com"
            }
        },
        {
            "sourceType": "iframe",
            "position": "right",
            "dimensions": {
                "height": 600,
                "width": 120
            },
            "iframe": {
                "source": "third.com"
            }
        },
        {
            "sourceType": "iframe",
            "position": "top",
            "dimensions": {
                "width": 728,
                "height": 90
            },
            "iframe": {
                "source": "first.com"
            }
        },
        {
            "sourceType": "background",
            "background": {
                "color": "#ccc"
            }
        }
    ]
}

findAll(Collections.ADS).then((ads) => {
    if (ads?.length === 0) {
        insert(Collections.ADS, [
            displaySelfhostedAd,
            display3rdPartyAd,
            outOfHomeVideoAd,
            outOfHomeVideoAdCoreBidderCreativeId1,
            videoSelfhostedAd,
            mobileExternalStickyFooter,
            displayFireplaceAd
        ])
    }
});
