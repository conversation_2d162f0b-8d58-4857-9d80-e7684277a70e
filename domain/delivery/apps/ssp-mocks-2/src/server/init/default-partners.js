import {insert, findAll} from "../datastore/mongo";
import {Collections} from "../constants/collections";
import {DEFAULT_PRESET_FILE_NAME} from "../constants/default-partner";
import logger from "../log/logger";

findAll(Collections.PARTNERS).then((partners) => {
    try {
        let jsonFile = require(`./presets/${DEFAULT_PRESET_FILE_NAME}`);
        if (partners && partners.length === 0) {
            for (let i = 0; i < jsonFile.partners.length; i++) {
                insert(Collections.PARTNERS, jsonFile.partners[i]);
            }
        }
    } catch (e) {
        logger.info(e);
        logger.error(e);
    }

});