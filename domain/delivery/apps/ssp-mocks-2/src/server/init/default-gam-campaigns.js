import {findAll, insert, upsert} from "../datastore/mongo";
import {Collections} from "../constants/collections";

const defaultGamCampaignResponse = {
    _id: "response", statusCode: 200, delayMs: 0, response: JSON.stringify({
        lastModification: new Date().getTime().toString(), bookedCampaigns: [{
            "gamId": "gam-id-cpm-0", "name": "I will be filtered out", "cpm": 0
        }, {
            "gamId": "221723335",
            "name": "2. Sonderfall_Default_4players_770x250",
            "contractId": "",
            "type": "programmatic",
            "mediaType": "display",
            "lastModification": "",
            "status": "DELIVERING",
            "pacing": "even",
            "cpm": 5,
            "boostedCpm": null,
            "gamCampaignType": "STANDARD",
            "costType": {
                "type": "CPM", "currencyCode": "EUR", "costPerUnit": 5
            },
            "budget": [{
                "currency": "EUR",
                "dailyBudget": null,
                "dailyClicks": null,
                "dailyImpressions": 100,
                "dailyQualifiedClicks": null,
                "totalBudget": 0,
                "totalClicks": null,
                "totalImpress ions": null,
                "totalQualifiedClicks": null
            }],
            "ads": [{
                "displayType": "standard", "dimensions": {
                    "width": 800, "height": 600
                }, "companions": [{
                    "displayType": "standard", "dimensions": {
                        "width": 300, "height": 200
                    }
                }]
            }],
            "endDate": null,
            "priority": 1,
            "startDate": "2014-07-08T10:06:00.000Z",
            "frequencyCapping": null,
            "lastModificationDate": "2014-07-23T08:35:55.000Z",
            "targeting": {
                "targetedAdUnits": [["p4444.4pl.4players.de_de"]], "excludedAdUnits": [["p4444.4pl.4players.de_de", "preroll"]], "customTargeting": {
                    "target": {
                        "anyOf": [{
                            "targetType": "audience", "include": {
                                "anyOf": [{
                                    "id": "547624759",
                                    "name": "Custom Segments > I_Luxusprodukten/Eigenheimer@ogm_nugg.ad_adex_data_li (57185)",
                                    "provider": "adex",
                                    "adexId": 57185,
                                    "type": "segment"
                                }, {
                                    "id": "410937822",
                                    "name": "Custom Segments > adex_IM_Eigenheimbesitzer (31138)",
                                    "provider": "adex",
                                    "adexId": 31138,
                                    "type": "segment"
                                }, {
                                    "id": "520015295",
                                    "name": "Custom Segments > adex:I_Renovierung_in_Eigenheim@adex_data_li (53322)",
                                    "provider": "adex",
                                    "adexId": 53322,
                                    "type": "segment"
                                }, {
                                    "id": "836035256",
                                    "name": "Custom Segments > OSDS > Customized Audience > Familien mit Wohneigentum + keine Schnäppchenjäger (113334)",
                                    "provider": "adex",
                                    "adexId": 113334,
                                    "type": "segment"
                                }, {
                                    "id": "520543267",
                                    "name": "Custom Segments > I_Gasheizung_in_Eigenheim@adex_data_li (53318)",
                                    "provider": "adex",
                                    "adexId": 53318,
                                    "type": "segment"
                                }, {
                                    "id": "566446423",
                                    "name": "Custom Segments > adex:I_Immobilien_Eigenheim@nugg.ad_adex_data_li (31127)",
                                    "provider": "adex",
                                    "adexId": 31127,
                                    "type": "segment"
                                }, {
                                    "id": "838030908",
                                    "name": "Custom Segments > OSDS > Customized Audience > Familien mit Wohneigentum + Alter 30-49 + Erwerbstätig + keine Schnäppchenjäger (113313)",
                                    "provider": "adex",
                                    "adexId": 113313,
                                    "type": "segment"
                                }]
                            }
                        }, {
                            "allOf": [{
                                "targetType": "context", "include": {
                                    "anyOf": [{
                                        "type": "keyValue", "key": "af", "value": "ds", "matcher": {
                                            "caseInsensitive": true
                                        }
                                    }]
                                }, "exclude": {
                                    "anyOf": [{
                                        "type": "keyValue", "key": "bf", "value": "ds", "matcher": {
                                            "caseInsensitive": true
                                        }
                                    }]
                                }
                            }]
                        }]
                    }
                }
            }
        }, {
            "gamId": "111784855",
            "name": "2. Sonderfall_D efault_caraworld.de_728x91",
            "contractId": "",
            "type": "programmatic",
            "mediaType": "display",
            "lastModification": "",
            "status": "DELIVERING",
            "pacing": "asap",
            "cpm": 10,
            "boostedCpm": null,
            "gamCampaignType": "SPONSORSHIP",
            "costType": {
                "type": "CPM", "currencyCode": "EUR", "costPerUnit": 10
            },
            "budget": [{
                "currency": "EUR",
                "dailyBudget": null,
                "dailyClicks": null,
                "dailyImpressions": 100,
                "dailyQualifiedClicks": null,
                "totalBudget": 0,
                "totalClicks": null,
                "totalImpressions": null,
                "totalQualifiedClicks": null
            }],
            "endDate": null,
            "priority": 1,
            "startDate": "2014-02-04T08:52:00.000Z",
            "frequencyCapping": null,
            "lastModificationDate": "2014-07-23T08:37:03.000Z",
            "targeting": {}
        }, {
            "gamId": "**********",
            "name": "m.MensHealth.de_Special_Advertorial exklusiv FP (Mobile)__a0G1v00001aTqRK",
            "contractId": "",
            "mediaType": "display",
            "lastModification": "",
            "status": "DELIVERING",
            "pacing": "frontloaded",
            "boostedCpm": null,
            "gamCampaignType": "SPONSORSHIP",
            "costType": {
                "type": "CPD", "currencyCode": "EUR", "costPerUnit": 5227.5
            },
            "budget": [{
                "currency": "EUR",
                "dailyBudget": null,
                "dailyClicks": null,
                "dailyImpressions": 100,
                "dailyQualifiedClicks": null,
                "totalBudget": 1594387.5,
                "totalClicks": null,
                "totalImpressions": null,
                "totalQualifiedClicks": null
            }],
            "endDate": null,
            "priority": 1,
            "startDate": "2014-02-04T08:52:00.000Z",
            "frequencyCapping": null,
            "lastModificationDate": "2014-07-23T08:37:03.000Z",
            "targeting": {
                "geo": {
                    "targetedCountries": ["DE", "AT"],
                    "excludedCountries": ["CZ"],
                    "targetedRegions": ["de_nw"],
                    "excludedRegions": ["de_sn"],
                    "targetedZipCodes": ["de_04279"],
                    "excludedZipCodes": ["de_10115"]
                }
            }
        },
        {
            "gamId": "234597678923",
            "name": "Test sponsorship campaign",
            "contractId": "",
            "mediaType": "display",
            "lastModification": "",
            "status": "DELIVERING",
            "pacing": "frontloaded",
            "boostedCpm": null,
            "gamCampaignType": "SPONSORSHIP",
            "costType": {
                "type": "CPD", "currencyCode": "EUR", "costPerUnit": 5227.5
            },
            "budget": [{
                "currency": "EUR",
                "dailyBudget": null,
                "dailyClicks": null,
                "dailyImpressions": 100,
                "dailyQualifiedClicks": null,
                "totalBudget": 1594387.5,
                "totalClicks": null,
                "totalImpressions": null,
                "totalQualifiedClicks": null
            }],
            "endDate": null,
            "priority": 1,
            "startDate": "2014-02-04T08:52:00.000Z",
            "frequencyCapping": null,
            "lastModificationDate": "2014-07-23T08:37:03.000Z",
            "targeting": {
                "targetedAdUnits": [["p4444.4pl.4players.de_de"]], "excludedAdUnits": [["p4444.4pl.4players.de_de", "preroll"]], "customTargeting": {
                    "target": {
                        "anyOf": [{
                            "targetType": "audience", "include": {
                                "anyOf": [{
                                    "id": "547624759",
                                    "name": "Custom Segments > I_Luxusprodukten/Eigenheimer@ogm_nugg.ad_adex_data_li (57185)",
                                    "provider": "adex",
                                    "adexId": 57185,
                                    "type": "segment"
                                }, {
                                    "id": "410937822",
                                    "name": "Custom Segments > adex_IM_Eigenheimbesitzer (31138)",
                                    "provider": "adex",
                                    "adexId": 31138,
                                    "type": "segment"
                                }, {
                                    "id": "520015295",
                                    "name": "Custom Segments > adex:I_Renovierung_in_Eigenheim@adex_data_li (53322)",
                                    "provider": "adex",
                                    "adexId": 53322,
                                    "type": "segment"
                                }, {
                                    "id": "836035256",
                                    "name": "Custom Segments > OSDS > Customized Audience > Familien mit Wohneigentum + keine Schnäppchenjäger (113334)",
                                    "provider": "adex",
                                    "adexId": 113334,
                                    "type": "segment"
                                }, {
                                    "id": "520543267",
                                    "name": "Custom Segments > I_Gasheizung_in_Eigenheim@adex_data_li (53318)",
                                    "provider": "adex",
                                    "adexId": 53318,
                                    "type": "segment"
                                }, {
                                    "id": "566446423",
                                    "name": "Custom Segments > adex:I_Immobilien_Eigenheim@nugg.ad_adex_data_li (31127)",
                                    "provider": "adex",
                                    "adexId": 31127,
                                    "type": "segment"
                                }, {
                                    "id": "838030908",
                                    "name": "Custom Segments > OSDS > Customized Audience > Familien mit Wohneigentum + Alter 30-49 + Erwerbstätig + keine Schnäppchenjäger (113313)",
                                    "provider": "adex",
                                    "adexId": 113313,
                                    "type": "segment"
                                }]
                            }
                        }, {
                            "allOf": [{
                                "targetType": "context", "include": {
                                    "anyOf": [{
                                        "type": "keyValue", "key": "af", "value": "ds", "matcher": {
                                            "caseInsensitive": true
                                        }
                                    }]
                                }, "exclude": {
                                    "anyOf": [{
                                        "type": "keyValue", "key": "bf", "value": "ds", "matcher": {
                                            "caseInsensitive": true
                                        }
                                    }]
                                }
                            }]
                        }]
                    }
                }
            }
        }]
    }, null, 2)
};

findAll(Collections.GAM_CAMPAIGNS).then((campaigns) => {
    if (campaigns && campaigns.length === 0) {
        insert(Collections.GAM_CAMPAIGNS, defaultGamCampaignResponse);
    }
});

export const resetGamCampaignResponse = async () => {
    await upsert(Collections.GAM_CAMPAIGNS, {_id: "response"}, defaultGamCampaignResponse)

    return defaultGamCampaignResponse;
}
