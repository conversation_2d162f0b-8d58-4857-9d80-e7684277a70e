import {findAll, insert} from "../datastore/mongo";
import {Collections} from "../constants/collections";

const now = new Date();
const nowPlus8Hours = new Date(now);
const nowMinus1Hour = new Date(now);
const nowPlus23Hours = new Date(now);
const nowPlus71Hours = new Date(now);
nowPlus8Hours.setUTCHours(nowPlus8Hours.getUTCHours() + 8);
nowMinus1Hour.setUTCHours(nowMinus1Hour.getUTCHours() - 1);
nowPlus23Hours.setUTCHours(nowPlus23Hours.getUTCHours() + 23);
nowPlus71Hours.setUTCHours(nowPlus71Hours.getUTCHours() + 71);

const coreBidderSponsorshipDisplayCampaign = {
    _id: "ee3f3f49-5dab-462c-8296-5cb90501d13b", statusCode: 200, delayMs: 0, response: JSON.stringify({
        lastModification: "MjExNjY1Ny0x",
        bookedCampaigns: [{
            "ibbId": "ee3f3f49-5dab-462c-8296-5cb90501d13b",
            "name": "2025_21176_0005 (3.9) Test TFP (400x200) (T Online Sport Desktop Run of Site) 282_216_7198700_009_BAUH-16772174",
            "contractId": "adscale.de",
            "associatedTargetingsId": null,
            "targeting": {
                "targetedAdUnits": [["p4444.4pl.4players.de_de"]], "geo": {
                    "excludedCountries": ["AQ"]
                }
            },
            "mediaType": "display",
            "frequencyCapping": [],
            "status": "DELIVERING",
            "lastModification": "MjExNjY1Ny0x",
            "startDate": nowMinus1Hour.toISOString(),
            "endDate": nowPlus23Hours.toISOString(),
            "priority": 14,
            "cpm": 0,
            "unitCost": {"cost": 1000000, "costType": "costPerDay"},
            "ads": [{
                "id": "core-bidder-sponsorship-display-creative-1",
                "type": "display",
                "targetedAdSlotFormats": [{
                    "format": "400x200"
                }],
                "dimensions": {
                    "height": 200,
                    "width": 400
                },
                "displayType": "standard"
            }],
            "budget": [],
            "pacing": "ASAP",
            "adExclusion": "one-per-page",
            "advertiserExclusion": {
                "excludedAdvertiserIds": [
                    "04f51a9e-ca6f-4ec3-ba49-06159e0e616d"
                ],
                "omitFromExclusion": false
            },
            "campaignAdvertiserId": "04f51a9e-ca6f-4ec3-ba49-06159e0e616d"
        }]
    }, null, 2)
};

const coreBidderSponsorshipDisplayFreqCapCampaign = {
    _id: "72650f16-c0e2-42e8-8683-35eb16eef203", statusCode: 200, delayMs: 0, response: JSON.stringify({
        lastModification: "MjExNjY1Ny0x",
        bookedCampaigns: [{
            "ibbId": "72650f16-c0e2-42e8-8683-35eb16eef203",
            "contractId": "adscale.de",
            "associatedTargetingsId": null,
            "targeting": {
                "targetedAdUnits": [
                    ["p4444.4pl.4players.de_de_fc"]
                ]
            },
            "frequencyCapping": [
                {
                    "type": "impressions",
                    "maximumEvents": 5,
                    "duration": "PT10M"
                }
            ],
            "budget": [], "pacing": "ASAP"
        }]
    }, null, 2)
};

const coreBidderSponsorshipDisplayCampaign2 = {
    _id: "1fdc02ca-c44d-4ef7-b384-cfd2a643bb0b", statusCode: 200, delayMs: 0, response: JSON.stringify({
        lastModification: "MjExNjY1Ny0x", bookedCampaigns: [{
            "ibbId": "1fdc02ca-c44d-4ef7-b384-cfd2a643bb0b",
            "name": "2025_21176_0005 (3.9) Test TFP-2 (400x200) (T Online Sport Desktop Run of Site) 282_216_7198700_009_BAUH-16772174",
            "contractId": "adscale.de",
            "associatedTargetingsId": null,
            "targeting": {
                "targetedAdUnits": [["p4444.4pl.4players.de_de2"]], "geo": {
                    "excludedCountries": ["AQ"]
                }
            },
            "mediaType": "display",
            "frequencyCapping": [],
            "status": "DELIVERING",
            "lastModification": "MjExNjY1Ny0x",
            "startDate": nowMinus1Hour.toISOString(), "endDate": nowPlus71Hours.toISOString(),
            "priority": 14,
            "cpm": 0,
            "unitCost": {"cost": 1000000, "costType": "costPerDay"},
            "ads": [{
                "id": "core-bidder-sponsorship-display-creative-2", "type": "display", "targetedAdSlotFormats": [{
                    "format": "400x200"
                }], "dimensions": {
                    "height": 200, "width": 400
                }, "displayType": "standard"
            }],
            "budget": [],
            "pacing": "ASAP",
            "adExclusion": "one-or-more",
            "advertiserExclusion": {
                "excludedAdvertiserIds": [
                    "04f51a9e-ca6f-4ec3-ba49-06159e0e616d"
                ],
                "omitFromExclusion": false
            },
            "campaignAdvertiserId": "4e808324-d11d-46ad-9c1b-58b117051692"
        }]
    }, null, 2)
};

const coreBidderSponsorshipDisplayMailAdCampaign = {
    _id: "77650f16-c0e2-42e8-8683-35eb16eef214", statusCode: 200, delayMs: 0, response: JSON.stringify({
        lastModification: "MjExNjY1Ny0x",
        bookedCampaigns: [{
            "ibbId": "77650f16-c0e2-42e8-8683-35eb16eef214",
            "contractId": "adscale.de",
            "associatedTargetingsId": null,
            "targeting": {
                "targetedAdUnits": [
                    ["stroeer_nz_adunit"]
                ]
            },
            "budget": [],
            "pacing": "ASAP"
        }]
    }, null, 2)
};

const coreBidderTestCampaign1 = {
    _id: "core-bidder-campaign-id-1", statusCode: 200, delayMs: 0, response: JSON.stringify({
        lastModification: "MjExNjY1Ny0x",
        bookedCampaigns: [{
            "ibbId": "core-bidder-campaign-id-1",
            "contractId": "cccd22e1-fa6c-41f6-83b2-b33c9f84084b",
            "associatedTargetingsId": "core-bidder-campaign-id-1",
            "targeting": {}
        }]
    }, null, 2)
};

const coreBidderVoyagerCampaign = {
    _id: "core-bidder-campaign-voyager", statusCode: 200, delayMs: 0, response: JSON.stringify({
        lastModification: "MjExNjY1Ny0x",
        bookedCampaigns: [{
            "ibbId": "core-bidder-campaign-voyager",
            "contractId": "cccd22e1-fa6c-41f6-83b2-b33c9f84084b",
            "associatedTargetingsId": "core-bidder-campaign-voyager",
            "targeting": {}
        }]
    }, null, 2)
};

const coreBidderTestCreativeTargetingCampaign1 = {
    _id: "core-bidder-targeting-campaign", statusCode: 200, delayMs: 0, response: JSON.stringify({
        lastModification: "MjExNjY1Ny0x",
        bookedCampaigns: [{
            "ibbId": "core-bidder-targeting-campaign",
            "contractId": "cccd22e1-fa6c-41f6-83b2-b33c9f84084b",
            "associatedTargetingsId": null,
            "targeting": {}
        }]
    }, null, 2)
};

const coreBidderPpvProgrammticGuaranteed = {
    _id: "ppv-trigger", statusCode: 200, delayMs: 0, response: JSON.stringify({
        lastModification: "MjExNjY1Ny0x",
        bookedCampaigns: [{
            "ibbId": "ppv-trigger",
            "contractId": "cccd22e1-fa6c-41f6-83b2-b33c9f84084b",
            "associatedTargetingsId": null,
            "targeting": {}
        }]
    }, null, 2)
};

const coreBidderDisplayAsapPacingCampaign = {
    _id: "a891d7f2-bcf1-4100-9268-ecee889ff96d", statusCode: 200, delayMs: 0, response: JSON.stringify({
        lastModification: "MjExNjY1Ny0x",
        bookedCampaigns: [{
            "ibbId": "a891d7f2-bcf1-4100-9268-ecee889ff96d",
            "contractId": "adscale.de",
            "associatedTargetingsId": null,
            "targeting": {
                "targetedAdUnits": [
                    ["stroeer_nz_pacing_adunit"]
                ]
            },
            "pacing": "ASAP",
            "status": "DELIVERING",
            "startDate": now.toISOString(),
            "endDate": nowPlus8Hours.toISOString(),
            "budget": [{
                "totalImpressions": 8, "comesFromCampaignId": "a891d7f2-bcf1-4100-9268-ecee889ff96d", "currency": "EUR"
            }]
        }]
    }, null, 2)
};

const coreBidderDisplayEvenPacingCampaign = {
    _id: "b4cdb736-80cc-41e5-a4aa-eb70deca8314", statusCode: 200, delayMs: 0, response: JSON.stringify({
        lastModification: "MjExNjY1Ny0x",
        bookedCampaigns: [{
            "ibbId": "b4cdb736-80cc-41e5-a4aa-eb70deca8314",
            "contractId": "adscale.de",
            "associatedTargetingsId": null,
            "targeting": {
                "targetedAdUnits": [
                    ["stroeer_nz_pacing_adunit"]
                ]
            },
            "pacing": "EVEN",
            "status": "DELIVERING",
            "startDate": now.toISOString(),
            "endDate": nowPlus8Hours.toISOString(),
            "budget": [{
                "totalImpressions": 960,
                "comesFromCampaignId": "b4cdb736-80cc-41e5-a4aa-eb70deca8314",
                "currency": "EUR"
            }]
        }]
    }, null, 2)
};

const coreBidderDisplayFrontloadedPacingCampaign = {
    _id: "da29b409-b961-4796-815b-114038070317", statusCode: 200, delayMs: 0, response: JSON.stringify({
        lastModification: "MjExNjY1Ny0x", bookedCampaigns: [{
            "ibbId": "da29b409-b961-4796-815b-114038070317",
            "contractId": "adscale.de",
            "associatedTargetingsId": null,
            "targeting": {
                "targetedAdUnits": [
                    ["stroeer_nz_pacing_adunit"]
                ]
            },
            "pacing": "FRONTLOADED",
            "status": "DELIVERING",
            "startDate": now.toISOString(),
            "endDate": nowPlus8Hours.toISOString(),
            "budget": [{
                "totalImpressions": 960,
                "comesFromCampaignId": "da29b409-b961-4796-815b-114038070317",
                "currency": "EUR"
            }]
        }]
    }, null, 2)
};

const emptyResponse = {
    _id: "empty", statusCode: 200, delayMs: 0, response: JSON.stringify({
        lastModification: "MjExNjY1Ny0x",
        bookedCampaigns: []
    }, null, 2)
};

const manyCampaigns = {
    _id: "many-campaigns", statusCode: 200, delayMs: 0, response: JSON.stringify({
        lastModification: "MjExNjY1Ny0x",
        bookedCampaigns: [
            {
                "ads": [
                    {
                        "id": "abc-123",
                        "targetedAdSlotFormats": [
                            {
                                "format": "728x90",
                            }
                        ],
                        "dimensions": {
                            "width": 728,
                            "height": 90
                        }
                    }
                ],
                "priority": 15,
                "cpm": 10.0,
                "status": "DELIVERING",
                "startDate": "2021-06-01T00:00:00Z",
                "endDate": "3001-06-30T23:59:59Z",
                "lastModification": "unsure",
                "ibbId": "ppv-trigger",
                "contractId": "cccd22e1-fa6c-41f6-83b2-b33c9f84084b",
                "associatedTargetingsId": null,
                "targeting": {}
            },
            {
                "ads": [
                    {
                        "id": "abc-123",
                        "targetedAdSlotFormats": [
                            {
                                "format": "728x90",
                            }
                        ],
                        "dimensions": {
                            "width": 728,
                            "height": 90
                        }
                    }
                ],
                "priority": 15,
                "cpm": 10.0,
                "status": "DELIVERING",
                "startDate": "2021-06-01T00:00:00Z",
                "endDate": "3001-06-30T23:59:59Z",
                "lastModification": "unsure",
                "ibbId": "ee3f3f49-5dab-462c-8296-5cb90501d13b",
                "contractId": "adscale.de",
                "associatedTargetingsId": null,
                "targeting": {
                    "targetedAdUnits": [
                        ["p4444.4pl.4players.de_de"]
                    ]
                },
                "frequencyCapping": []
            }
        ]
    }, null, 2)
}

findAll(Collections.BOOKED_CAMPAIGNS).then((campaigns) => {
    if (campaigns && campaigns.length === 0) {
        insert(Collections.BOOKED_CAMPAIGNS, coreBidderSponsorshipDisplayCampaign);
        insert(Collections.BOOKED_CAMPAIGNS, coreBidderSponsorshipDisplayFreqCapCampaign);
        insert(Collections.BOOKED_CAMPAIGNS, coreBidderSponsorshipDisplayMailAdCampaign);
        insert(Collections.BOOKED_CAMPAIGNS, coreBidderTestCampaign1);
        insert(Collections.BOOKED_CAMPAIGNS, coreBidderVoyagerCampaign);
        insert(Collections.BOOKED_CAMPAIGNS, coreBidderTestCreativeTargetingCampaign1);
        insert(Collections.BOOKED_CAMPAIGNS, coreBidderPpvProgrammticGuaranteed);
        insert(Collections.BOOKED_CAMPAIGNS, coreBidderDisplayAsapPacingCampaign);
        insert(Collections.BOOKED_CAMPAIGNS, coreBidderDisplayEvenPacingCampaign);
        insert(Collections.BOOKED_CAMPAIGNS, coreBidderDisplayFrontloadedPacingCampaign);
        insert(Collections.BOOKED_CAMPAIGNS, emptyResponse);
        insert(Collections.BOOKED_CAMPAIGNS, manyCampaigns);
        insert(Collections.BOOKED_CAMPAIGNS, coreBidderSponsorshipDisplayCampaign2);
    }
});
