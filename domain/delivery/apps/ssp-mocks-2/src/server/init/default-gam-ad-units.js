import {insert, findAll, upsert} from "../datastore/mongo";
import {Collections} from "../constants/collections";

const defaultGamAdUnitsResponse = {
    _id: "response", statusCode: 200, delayMs: 0, response: JSON.stringify({
        itemsCount: 3, adUnits: [{
            "adUnitId": "28031215",
            "adUnitCode": "EssenTrinken",
            "adUnitCodePath": ["p4444.si.dhd24.com_de", "EssenTrinken"],
            "explicitlyTargeted": true,
            "lastModificationDate": "2022-07-05T00:00:00.000Z",
        }, {
            "adUnitId": "35342355",
            "adUnitCode": "speciala",
            "adUnitCodePath": [
                "menshealth.de_sd",
                "speciala"
            ],
            "explicitlyTargeted": true,
            "lastModificationDate": "2022-07-05T00:00:00.000Z",
        }, {
            "adUnitId": "35342356",
            "adUnitCode": "specialb",
            "adUnitCodePath": [
                "womenshealth.de_sd",
                "specialb"
            ],
            "explicitlyTargeted": true,
            "lastModificationDate": "2022-07-05T00:00:00.000Z",
        }]
    }, null, 2)
};

findAll(Collections.GAM_AD_UNITS).then((adunits) => {
    if (adunits && adunits.length === 0) {
        insert(Collections.GAM_AD_UNITS, defaultGamAdUnitsResponse);
    }
});

export const resetGamAdUnitsResponse = async () => {
    await upsert(Collections.GAM_AD_UNITS, {_id: "response"}, defaultGamAdUnitsResponse)

    return defaultGamAdUnitsResponse;
}
