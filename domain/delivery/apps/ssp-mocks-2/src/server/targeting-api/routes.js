import express from 'express';

const apiRoutes = express.Router();

apiRoutes.get('/creators', (req, res) => {
    const entityId = req.query.entityId;
    const creators = [];
    if (entityId) creators.push({ id: entityId, entityId: entityId })
    const response = { creators };
    res.status(200).send(response);
});

apiRoutes.get('/associations/:creator/targetings', (req, res) => {
    const creator = req.params.creator;

    switch (creator) {
        case 'core-bidder-campaign-voyager':
            res.status(200).send({
                targetings: [{
                    id: "12d7d3cc-872a-4c6e-8a77-9de437e3033a",
                    type: "inventory",
                    deleted: false,
                    data: {
                        operator: "and",
                        conditions: [{
                            type: "site",
                            condition: {
                                "in": ["9867551c-fc9b-4868-8582-9aee445cda0b"]
                            }
                        }]
                    }
                }]
            });
            break;
        case 'core-bidder-campaign-id-1':
            res.status(200).send({
                targetings: [{
                    id: "12d7d3cc-872a-4c6e-8a77-9de437e3033a",
                    type: "inventory",
                    deleted: false,
                    data: {
                        operator: "and",
                        conditions: [{
                            type: "site",
                            condition: {
                                "in": ["222200b2-5db1-415e-b0e4-3d218e4a6602"]
                            }
                        }]
                    }
                }]
            });
            break;

        default:
            const targetings = {
                targetings: []
            };
            res.status(200).send(targetings);
            break;
    }
});

export default apiRoutes;
