import express from "express";
import {insert, upsert, update, findAll, remove} from "../datastore/mongo";
import {ObjectId} from 'mongodb';
import {Collections} from "../constants/collections";
import {EventStore} from '../redux/events';
import {RequestType} from "../constants/request-type";
import logger from "../log/logger";
import {DEFAULT_PRESET_FILE_NAME, DEFAULT_PARTNER} from "../constants/default-partner"

let fs = require("fs");
const path = require('path');

const apiRoutes = express.Router();
const PRESET_DIRECTORY = process.env.PRESET_DIRECTORY

apiRoutes.get('/partners', (req, res) => {
    findAll(Collections.PARTNERS)
            .then((partners) => {
                res.send(partners);
            })
});

function partnerQuery(partner) {
    // partner._id is only defined if /partners is called from the mocks UI
    if (partner._id === undefined) {
        // for pytest we lookup existing partners by name
        return {name: {$eq: partner.name}}
    }
    partner._id = ObjectId(partner._id);
    return {_id: {$eq: partner._id}};
}

apiRoutes.post('/partners', (req, res) => {
    logger.debug(`Hitting POST /api/partners`);
    let partner = req.body.partner;
    if (req.body.deletingResponse) {
        logger.info(`Deleting response (${req.body.responseName}) for partner: ${partner.name}`);
    }
    upsert(Collections.PARTNERS, partnerQuery(partner), partner)
            .then((inserted) => {
                res.send("Upserted: " + inserted + " entry into " + Collections.PARTNERS);
                EventStore.dispatch({type: 'CHANGED', collection: Collections.PARTNERS});
            })
});

// Takes the partner to reset and the last loaded preset (if any)
// Resets the responses of the given partner to the last state saved in that preset (or default-preset if no preset has been loaded)
apiRoutes.post('/reset', (req, res) => {
    logger.debug(`Hitting POST /api/reset`);
    let fileName;
    try {
        fileName = req.body.preset.fileName;
    } catch {
        fileName = DEFAULT_PRESET_FILE_NAME;
    }
    let partner = req.body.partner;
    let jsonFile = require(`${PRESET_DIRECTORY}/${fileName}`);
    try {
        partner.responses = jsonFile.partners.find(p => p.name === partner.name).responses;
    } catch {
        partner.responses = DEFAULT_PARTNER.responses
    }
    upsert(Collections.PARTNERS, partnerQuery(partner), partner)
            .then((inserted) => {
                logger.info(`Resetting partner: ${partner.name}`);
                res.send("Upserted: " + inserted + " entry into " + Collections.PARTNERS);
                EventStore.dispatch({type: 'CHANGED', collection: Collections.PARTNERS});
            })
});

apiRoutes.delete('/partner', (req, res) => {
    logger.debug(`Hitting DELETE /api/partner`);
    let partner = req.body.partner;
    remove(Collections.PARTNERS, {name: {$eq: partner.name}})
            .then((deleted) => {
                logger.info(`Deleted partner ${partner.name}`);
                res.send("Deleted: " + deleted + " from " + Collections.PARTNERS);
                EventStore.dispatch({type: 'CHANGED', collection: Collections.PARTNERS});
            })
});

apiRoutes.get('/bids', (req, res) => {
    logger.debug(`Hitting GET /api/bids`);
    findAll(Collections.REQUESTS, {type: {$eq: RequestType.BID}}, {timestamp: -1}, 100)
            .then((reqs) => {
                res.send(reqs);
            })
});

apiRoutes.get('/notification', (req, res) => {
    logger.debug(`Hitting GET /api/notification`);
    findAll(Collections.REQUESTS, {type: {$eq: RequestType.NOTIFY}}, {timestamp: -1}, 100)
            .then((reqs) => {
                res.send(reqs);
            })
});

apiRoutes.get('/:partner/notification', (req, res) => {
    logger.debug(`Hitting GET /api/:partner/notification`);
    findAll(Collections.REQUESTS, {type: {$eq: RequestType.NOTIFY}, partnerName: {$eq: req.params.partner}}, {}, 100)
            .then((reqs) => {
                res.send(reqs);
            })
});

apiRoutes.delete('/:partner/notification', (req, res) => {
    logger.debug(`Hitting DELETE /api/:partner/notification`);
    remove(Collections.REQUESTS, {type: {$eq: RequestType.NOTIFY}, partnerName: {$eq: req.params.partner}})
            .then((removed) => {
                let partner = findAll(Collections.PARTNERS, {partnerName: {$eq: req.params.partner}});
                update(Collections.PARTNERS,
                        {partnerName: {$eq: req.params.partner}},
                        {$set: {requests: {$eq: partner.requests} - removed.nRemoved}});
                res.send("Removed: " + removed);
            });
});

apiRoutes.get('/requests', (req, res) => {
    logger.debug(`Hitting GET /api/requests`);
    findAll(Collections.REQUESTS, {}, {timestamp: -1}, 100)
            .then((reqs) => {
                res.send(reqs);
            })
});

apiRoutes.post('/requests', (req, res) => {
    logger.debug(`Hitting POST /api/requests`);
    insert(Collections.REQUESTS, req.body)
            .then((inserted) => {
                res.send("Inserted: " + inserted + " entry into " + Collections.REQUESTS);
            })
});

apiRoutes.delete('/requests', (req, res) => {
    logger.debug(`Hitting DELETE /api/requests`);
    remove(Collections.REQUESTS)
            .then((removed) => {
                res.send("Removed: " + removed);
            });
    update(Collections.PARTNERS, {}, {$set: {requests: 0}});
    update(Collections.PARTNERS, {}, {$set: {notifications: 0}});
});

apiRoutes.get('/:partner/requests', (req, res) => {
    logger.debug(`Hitting GET /api/:partner/requests`);
    let partner = req.params.partner;
    findAll(Collections.REQUESTS, {type: {$eq: RequestType.BID}, partnerName: {$eq: partner}}, {}, 100)
            .then((fetched) => {
                res.send(fetched);
            })
});

apiRoutes.delete('/:partner/requests', (req, res) => {
    logger.debug(`Hitting DELETE /api/:partner/requests`);
    let partner = req.params.partner;
    remove(Collections.REQUESTS, {type: {$eq: RequestType.BID}, partnerName: {$eq: partner}})
            .then((deleted) => {
                let partner = findAll(Collections.PARTNERS, {partnerName: {$eq: partner}});
                update(Collections.PARTNERS,
                        {partnerName: {$eq: req.params.partner}},
                        {$set: {requests: {$eq: partner.requests} - deleted.nRemoved}});
                res.send("Deleted: " + deleted + " from " + Collections.REQUESTS);
                EventStore.dispatch({type: 'CHANGED', collection: Collections.REQUESTS});
            })
});

apiRoutes.get('/templates', (req, res) => {
    logger.debug(`Hitting GET /api/templates`);
    findAll(Collections.TEMPLATES)
            .then((partners) => {
                res.send(partners);
            })
});

let simpleResp = {statusCode: 200, body: "<html><body></body></html>", headers: {"Content-Type": "text/html"}};

let okayResponse = {
    statusCode: 200, body: {
        response: {
            "status": "OK"
        }
    }
};

apiRoutes.get('/simple', (req, res) => {
    res.set(simpleResp.headers)
    res.status(simpleResp.statusCode)
    res.send(simpleResp.body);
});

apiRoutes.put('/simple', (req, res) => {
    simpleResp = req.body;
    res.send(okayResponse);
});

apiRoutes.post('/simple', (req, res) => {
    simpleResp = req.body;
    res.send(okayResponse);
});

apiRoutes.get('/:partner/usermatch', (req, res) => {
    let adscaleUid = req.query.uid;
    let partnerId = req.query.tpid;
    // let cburl = new Buffer(this.queryParams.cburl, 'base64');
    let cburl = decodeURI(req.query.cburl);
    let tpuid = adscaleUid + "-" + partnerId;

    let redirect_url = cburl + "?tpid=" + partnerId + "&tpuid=" + tpuid;
    res.redirect(302, redirect_url);
});

apiRoutes.post('/:partner/usermatch', (req, res) => {
    res.send(okayResponse);
});

// Deletes all partners
apiRoutes.delete('/partners', (req, res) => {
    logger.debug(`Hitting DELETE /api/partners`);
    remove(Collections.PARTNERS, {})
            .then((deleted) => {
                logger.info(`Deleted all partners`);
                res.send("Deleted: " + deleted + " from " + Collections.PARTNERS);
                EventStore.dispatch({type: 'CHANGED', collection: Collections.PARTNERS});
            })
});

// Loads a preset populated with partners from a named json file into the mongo database
apiRoutes.post('/presets/:fileName', (req, res) => {
    let fileName = req.params.fileName;
    let jsonFile = JSON.parse(fs.readFileSync(`${PRESET_DIRECTORY}/${fileName}`));
    logger.info("Loading preset from: " + `${PRESET_DIRECTORY}/${fileName}`)
    findAll(Collections.PARTNERS).then(() => {
        for (let i = 0; i < jsonFile.partners.length; i++) {
            insert(Collections.PARTNERS, jsonFile.partners[i]);
        }
    }).then(() => {
        res.send(`Loaded partners from preset: ${jsonFile.name}`);
    })
});

// Saves a preset as a JSON file in ../init/presets
apiRoutes.put('/presets', (req, res) => {
    try {
        let body = req.body;
        if (body.preset.fileName === DEFAULT_PRESET_FILE_NAME) {
            res.status(400).send("Can't save over the default preset (fileName: 'default-preset.json' is reserved)")
        }
        body.preset.partners.map(partner => { if (partner._id) { delete partner._id }});
        let preset = JSON.stringify(body.preset);
        fs.writeFileSync(`${PRESET_DIRECTORY}/${body.preset.fileName}`, preset);
        logger.info("Saving preset to: " + `${PRESET_DIRECTORY}/${body.preset.fileName}`)
        res.send(okayResponse);
    } catch (e) {
        logger.info(`Error saving a preset to json file:\n${e}`);
        res.send(`Error saving a preset to json file:\n${e}`);
    }
})

// Gets all files in ../init/presets, parses them as JSON objects and returns an array with all of those object
apiRoutes.get('/presets', (req, res) => {
    logger.debug(`Hitting GET /api/presets`);
    let presets = [];
    const dirPath = path.resolve(`${PRESET_DIRECTORY}`);
    let fileNames = fs.readdirSync(dirPath);
    for (let i = 0; i < fileNames.length; i++) {
        let filePath = `${PRESET_DIRECTORY}/${fileNames[i]}`;
        let file = fs.readFileSync(filePath);
        let jsonFile = JSON.parse(file);
        delete jsonFile.partners;
        presets.push(jsonFile)
    }
    res.send(presets);
});

apiRoutes.delete('/presets/:fileName', (req, res) => {
    logger.debug(`Hitting DELETE /api/presets`);
    let fileName = req.params.fileName;
    if (fileName === DEFAULT_PRESET_FILE_NAME) {
        res.status(400).send("Can't delete the default preset (fileName: 'default-preset.json' is reserved)")
    }
    const filePath = `${PRESET_DIRECTORY}/${fileName}`;
    logger.info("Deleting preset from: " + filePath)
    fs.unlink(filePath, function (err) {
        if (err) {
            console.error(err);
            res.send(err.message);
        } else {
            res.status(200).send(`File removed: ${filePath}`);
        }
    });
})

apiRoutes.get('/environment', (req, res) => {
    logger.debug(`Hitting GET /api/environment`);
    logger.debug(`Current environment detected as: ${process.env.CONFIG_ENVIRONMENT_NAME}`)
    res.status(200).send(process.env.CONFIG_ENVIRONMENT_NAME || "dev");
})

export default apiRoutes;
