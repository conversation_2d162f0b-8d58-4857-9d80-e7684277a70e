import express from "express";
import {find, findAll} from '../datastore/mongo'
import {Collections} from "../constants/collections";


const router = express.Router();

function getConditionsForAds(query) {
    return {
        ...(query.adIds && {id: {$in: query.adIds.split(',')}}),
        ...(query.changedSince && {_timestamp: {
            $gt: parseInt(query.changedSince)
        }})
    };
}
function getConditionsForAssociations(query) {
    return {
        ...(query.adIds && {adId: {$in: query.adIds.split(',')}}),
        ...(query.updatedSince && {lastModification: {
            $gt: parseInt(query.updatedSince)
        }})
    };
}

router.get('/ads', async (request, response) => {
    const ads = await findAll(Collections.ADS, getConditionsForAds(request.query), {_timestamp: 1})

    response.json(ads)
});

router.get('/ad-associations', async (request, response) => {
    const adAssociations = await findAll(Collections.AD_ASSOCIATIONS, getConditionsForAssociations(request.query), {lastModification: 1})

    response.json({adAssociations});
});

export default router;
