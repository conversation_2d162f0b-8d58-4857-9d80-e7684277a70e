import express from 'express';
import {find} from '../datastore/mongo'
import {Collections} from "../constants/collections";

const router = express.Router();

router.get("/booked-campaigns", async (req, res) => {
    const query = {
        _id: req.query.ibbId ?? "many-campaigns"
    }

    let savedResponse = await find(Collections.BOOKED_CAMPAIGNS, query)

    if (!savedResponse) {
        savedResponse = await find(Collections.BOOKED_CAMPAIGNS, {_id: "empty"})
    }

    setTimeout(() => {
        return res.status(savedResponse.statusCode).send(savedResponse.response)
    }, savedResponse.delayMs)
});

router.get("/campaigns/:campaignId/targetings", async (req, res) => {
    const { campaignId } = req.params

    let savedResponse = await find(Collections.BOOKED_CAMPAIGNS, {_id: campaignId})
    let targeting;

    if (savedResponse) {
        let parsedResponse = JSON.parse(savedResponse.response);
        targeting = parsedResponse.bookedCampaigns?.[0]?.targeting;
    }
    else {
        savedResponse = await find(Collections.BOOKED_CAMPAIGNS, {_id: "empty"})
    }

    setTimeout(() => {
        return res.status(savedResponse.statusCode).send(targeting)
    }, savedResponse.delayMs)
});

export default router;
