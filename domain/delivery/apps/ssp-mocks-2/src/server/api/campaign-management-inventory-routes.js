import express from "express";
import {find, upsert} from '../datastore/mongo'
import {Collections} from "../constants/collections";
import {resetGamCampaignResponse} from "../init/default-gam-campaigns";
import {resetGamAdUnitsResponse} from "../init/default-gam-ad-units";
import axios from "axios";

const pathOfGamCampaignResponseStub = "/api/campaign/response-stub";

const pathOfGamAdUnitsResponseStub = "/api/adunits/response-stub";

const router = express.Router();

const notifyGcmGamCampaigns = () => {
    axios.post("http://gcm.lsd.test/admin/refresh-booked-campaigns")
            .then(() => console.log("Gcm notified about GAM campaign changes"))
            .catch((err) => console.error(err))
}

const notifyGcmGamAdUnits = () => {
    axios.post("http://gcm.lsd.test/admin/refresh-ad-units")
            .then(() => console.log("Gcm notified about ad unit changes"))
            .catch((err) => console.error(err))
}

router.get('/api/gam-booked-campaigns', async (request, response) => {
    const savedResponse = await find(Collections.GAM_CAMPAIGNS, {_id: "response"})

    setTimeout(() => {
        return response.status(savedResponse.statusCode).send(savedResponse.response)
    }, savedResponse.delayMs)
});

router.get('/api/gam-ad-units', async (request, response) => {
    const savedResponse = await find(Collections.GAM_AD_UNITS, {_id: "response"})

    setTimeout(() => {
        return response.status(savedResponse.statusCode).send(savedResponse.response)
    }, savedResponse.delayMs)
});

router.get(pathOfGamCampaignResponseStub, async (request, response) => {
    const savedResponse = await find(Collections.GAM_CAMPAIGNS, {_id: "response"})
    response.send(savedResponse);
});

router.post(pathOfGamCampaignResponseStub, async (request, response) => {
    await upsert(Collections.GAM_CAMPAIGNS, {_id: "response"}, request.body);
    notifyGcmGamCampaigns();
    response.sendStatus(200).end();
});

router.delete(pathOfGamCampaignResponseStub, async (request, response) => {
    const gamCampaignResponse = await resetGamCampaignResponse();
    notifyGcmGamCampaigns();
    response.send(gamCampaignResponse);
});

router.get(pathOfGamAdUnitsResponseStub, async (request, response) => {
    const savedResponse = await find(Collections.GAM_AD_UNITS, {_id: "response"})
    response.send(savedResponse);
});

router.post(pathOfGamAdUnitsResponseStub, async (request, response) => {
    await upsert(Collections.GAM_AD_UNITS, {_id: "response"}, request.body);
    notifyGcmGamAdUnits();
    response.sendStatus(200).end();
});

router.delete(pathOfGamAdUnitsResponseStub, async (request, response) => {
    const gamAdUnitResponse = await resetGamAdUnitsResponse();
    notifyGcmGamAdUnits();
    response.send(gamAdUnitResponse);
});

export default router;