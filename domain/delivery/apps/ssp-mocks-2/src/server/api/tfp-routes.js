import express from 'express';
import {findOneToMany} from '../datastore/mongo';
import {Collections} from "../constants/collections";

const router = express.Router();

router.get("/booked-campaigns", async (req, res) => {
    const ibbId = req.query.ibbId;

    const query = ibbId ? {_id: ibbId} : {
        _id: {
            $in: ["ee3f3f49-5dab-462c-8296-5cb90501d13b", "1fdc02ca-c44d-4ef7-b384-cfd2a643bb0b"]
        }
    };

    const savedResponses = await findOneToMany(Collections.BOOKED_CAMPAIGNS, query);

    if (!savedResponses || savedResponses.length === 0) {
        console.log("response from tfp/booked-campaigns is not OK");
        return res.status(404).send({error: "Booked campaigns not found"});
    }
    const responses = savedResponses.map(entry => JSON.parse(entry.response));

    const allBookedCampaigns = responses.flatMap(res => res.bookedCampaigns ?? []);
    const lastModification = responses[0].lastModification;

    setTimeout(() => {
        res.status(200).send({
            lastModification, bookedCampaigns: allBookedCampaigns
        });
    }, 0);
});

export default router;
