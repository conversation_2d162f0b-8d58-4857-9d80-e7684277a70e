import io from 'socket.io';
import {EventStore} from '../redux/events';

let wss;

export function createWss(server, context) {
    wss = io(server, {path: context});
    wss.on('connection', function(client){
        client.on('event', function(data){});
        client.on('disconnect', function(){});
    });

    EventStore.subscribe(() => {
        const collection = EventStore.getState();
        wss.emit('collection', {name: collection});
    })
}

