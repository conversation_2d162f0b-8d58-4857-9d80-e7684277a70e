import {Collections} from "../constants/collections";
import {find} from "../datastore/mongo";
import {EventStore} from '../redux/events';
import logger from "../log/logger";

const responses = {};

export function getResponseForPartner(partnerName, responseIndex) {
    logger.info("getting response for partner " + partner<PERSON><PERSON> + " at index " + responseIndex);
    let cached = responses[`${partnerName}~${responseIndex}`];
    if (cached) {
        logger.debug("found cached response: " + JSON.stringify(cached));
        return Promise.resolve(cached);
    }
    else {
        return find(Collections.PARTNERS, {name: {$eq: partnerName}}).then((partnerObj) => {
            const responseObj = partnerObj.responses[responseIndex].response;
            logger.debug("found response from db: " + JSON.stringify(responseObj));
            updateCache(partnerName, responseIndex,responseObj);
            return responseObj
        }).catch((err) => {
            throw err;
        });
    }

}

EventStore.subscribe(() => {
    const collection = EventStore.getState();
    logger.debug("received update message: " +  collection);
    if (collection === 'partners') {
        for (let k in responses) {
            let split = k.split('~');
            logger.debug("updating partner : " + split[0] + " response to: " + JSON.stringify(responses[k]));
            find(Collections.PARTNERS, {name: {$eq: split[0]}}).then((partnerObj) => {
                try {
                    responses[k] = partnerObj.responses[parseInt(split[1])].response;
                } catch (e) {
                    if (e instanceof TypeError) {
                        delete responses[k];
                    }
                }

            })
        }
    }
});


function updateCache(partnerName, responseIndex, responseObj) {
    responses[`${partnerName}~${responseIndex}`] = responseObj;
}