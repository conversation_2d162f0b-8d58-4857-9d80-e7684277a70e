import {Collections} from "../constants/collections";
import {RequestType} from "../constants/request-type";
import {count, find, insert, upsert} from "../datastore/mongo";
import {EventStore} from '../redux/events';
import {getResponseForPartner} from "./ResponseService";
import logger from "../log/logger";

function recordRequest(partnerName, req, resp) {
    let requestObj = {
        timestamp: new Date(), partnerName: partnerName, type: RequestType.BID, url: req.path, request: req.body, headers: req.headers, response: resp
    };
    saveRequest(requestObj).then(() => {
        updateRequestCount(requestObj.partnerName);
    });
}

export function sendBid(req, res) {
    let name = req.params.partner;
    let conditionalMet = false;
    logger.info("received bid request to partner: " + name);
    find(Collections.PARTNERS, {name: {$eq: name}}).then((partnerObj) => {
        if (partnerObj === null) {
            logger.warn("Partner does not exist: " + name);
            return res.sendStatus(400);
        }

        logger.debug("partnerObj: " + partnerObj);
        for (let index = 0; index < partnerObj.responses.length; index++) {
            if (shouldSendBid(partnerObj.responses[index], req.body) && !conditionalMet) {
                conditionalMet = true;
                getResponseForPartner(name, index).then((responseObj) => {
                    let statusCode = partnerObj.responses[index].statusCode;
                    let finalResponse = replaceMacro(req, responseObj);

                    res.status(statusCode);
                    res.json(finalResponse);
                    return finalResponse;
                }).then((responseObj) => {
                    recordRequest(name, req, responseObj);
                }).catch((err) => {
                    logger.error(err);
                    recordRequest(name, req, {"statusCode": 204, "message": "Error processing request: " + err});
                    return res.sendStatus(204);
                });
            } else {
                if (index === partnerObj.responses.length - 1 && !conditionalMet) {
                    logger.info("conditionals exhausted - not bidding on request");
                    recordRequest(name, req, {"statusCode": 204, "message": "No conditionals met"});
                    return res.sendStatus(204);
                }
            }
        }
    }).catch(e => {
        logger.error(e);
        recordRequest(name, req, {"statusCode": 400, "message": "Error processing request: " + e});
        return res.sendStatus(400);
    });
}

export function onNotify(req, res) {
    let name = req.params.partner;
    logger.info("received notification for partner: " + name);
    let notifyObj = {timestamp: new Date(), partnerName: name, type: RequestType.NOTIFY, url: req.path, request: req.body};
    res.sendStatus(200);
    find(Collections.PARTNERS, {name: name}).then(response => {
        if (response) {
            insert(Collections.REQUESTS, notifyObj).then(() => {
                updateNotificationCount(name);
                logger.debug("notification saved to db: " + JSON.stringify(notifyObj));
            }).catch((ex) => {
                logger.error("cannot save notification to database", ex);
            });
        } else {
            logger.warn("Request to partner " + name + " has been received, but no such partner exists in the database.")
        }
    });
}

function shouldSendBid(partnerObj, requestBody) {
    const isRequestFromPrebidServer = requestBody.ext !== undefined && requestBody.ext.prebid !== undefined;

    if (isRequestFromPrebidServer !== !!partnerObj.prebidServer) {
        return false;
    }

    if (!partnerObj.conditional) {
        return true;
    }

    let func = () => {
        return true
    };
    if (partnerObj.conditionalFunc !== undefined) {
        logger.info("partnerObj.conditionalFunc: " + partnerObj.conditionalFunc);
        func = eval("(" + partnerObj.conditionalFunc + ")");
    }
    try {
        logger.info("requestBody: " + JSON.stringify(requestBody));
        return func(requestBody);
    }
    catch (e) {
        logger.error("Failed to run conditional check", e);
        return false;
    }

}

function saveRequest(requestObj) {
    logger.info("saving request for partner : " + requestObj.partnerName + " request: " + JSON.stringify(requestObj));
    return insert(Collections.REQUESTS, requestObj);
}

function updateRequestCount(partner) {
    count(Collections.REQUESTS, {partnerName: partner, type: RequestType.BID}).then((numReq) => {
        logger.debug("updating request count for partner ");
        upsert(Collections.PARTNERS, {name: partner}, {$set: {requests: numReq}});
    }).then(() => {
        EventStore.dispatch({type: 'CHANGE', collection: Collections.PARTNERS});
    })
}

function updateNotificationCount(partner) {
    count(Collections.REQUESTS, {partnerName: partner, type: RequestType.NOTIFY}).then((numNotify) => {
        logger.debug("updating notification count for partner ");
        upsert(Collections.PARTNERS, {name: partner}, {$set: {notifications: numNotify}});
        logger.info(numNotify.toString() + " notifications for " + partner);
    }).then(() => {
        EventStore.dispatch({type: 'CHANGE', collection: Collections.PARTNERS});
    })
}

function replaceMacro(req, response) {
    let width, height, host;
    let rtbRequest = req.body;
    try {
        width = rtbRequest.imp[0].banner.w;
        if (!width && rtbRequest.imp[0].banner.format) {
            width = rtbRequest.imp[0].banner.format[0].w
        }
        height = rtbRequest.imp[0].banner.h;
        if (!height && rtbRequest.imp[0].banner.format) {
            height = rtbRequest.imp[0].banner.format[0].h
        }
        host = process.env.HOST ? process.env.HOST : req.headers.host;
        logger.debug("Found fields on request - w:" + width + " h: " + height + " host: " + host)
    }
    catch (ex) {
        console.log(ex);
    }
    let responseStr = JSON.stringify(response);
    if (width) {
        responseStr = responseStr.replace(new RegExp("__WIDTH__", 'g'), width);
    }
    if (height) {
        responseStr = responseStr.replace(new RegExp("__HEIGHT__", 'g'), height);
    }
    if (host) {
        responseStr = responseStr.replace(new RegExp("__HOST__", 'g'), host);
    }
    try {
        let bidRequestStr = JSON.stringify(rtbRequest)
        bidRequestStr = bidRequestStr.replace(new RegExp('"', 'g'), '\\"');
        responseStr = responseStr.replace(new RegExp("__BID_REQUEST__", 'g'), bidRequestStr);
    } catch (e) {
        console.error("Failed to replace bid request macro in response", e)
    }
    logger.debug("Replaced macros: " + responseStr);
    return JSON.parse(responseStr);
}
