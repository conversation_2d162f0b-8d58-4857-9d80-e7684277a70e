export const DEFAULT_PARTNER = {
    "name": "default",
    "endpoint": "http://__HOST__DOMAIN__:__PORT__/rtb/default/bid",
    "requests": 0,
    "responses": [{
        "prebidServer": false,
        "conditional": false,
        "conditionalFunc": "function shouldRespond(request) { return true; }",
        "statusCode": 200,
        "name": "Default Response",
        "response": {
            "id": "393", "bidid": "8284", "cur": "US", "customdata": "some custom data", "seatbid": [{
                "group": 0, "bid": [{
                    "id": "bidder id1",
                    "impid": "1",
                    "price": "5.57",
                    "adid": "2822",
                    "adm": "<script id='mocks-advert' data-content='Buy Me __WIDTH__x__HEIGHT__ Price macros clear: ${AUCTION_PRICE} encrypted: ${AUCTION_PRICE:ENC} TCF Macros applies: ${GDPR} personal-data: ${GDPR_PD}' data-width=__WIDTH__ data-height=__HEIGHT__ data-css-link='https://__HOST__/ad.css' src='https://__HOST__/advert.js'></script>",
                    "crid": "creative id1",
                    "w": "__WIDTH__",
                    "h": "__HEIGHT__",
                    "adomain": ["domain1.com", "domain2.com"],
                    "ext": {
                        "avn": "adv1488848261", "agn": "agn881772483"
                    }
                }]
            }]
        }
    },]
};

export const DEFAULT_PRESET_FILE_NAME = "default-preset.json";
