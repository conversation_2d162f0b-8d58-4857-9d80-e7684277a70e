import {MongoClient} from 'mongodb';
import co from 'co';
import {EventStore} from '../redux/events';

const MONGODB_USERNAME = process.env.MONGODB_USERNAME || 'mocks';
const MONGODB_PASSWORD = process.env.MONGODB_PASSWORD || 'password';
const MONGODB_HOST = process.env.MONGODB_HOST || 'mongo.service.ssp.consul';
const MONGODB_PORT = process.env.MONGODB_PORT || '27017';
const MONGODB_DATABASE = process.env.MONGODB_DATABASE || 'mocks';
const MONGODB_CONNECTION_OPTIONS = process.env.MONGODB_CONNECTION_OPTIONS || '';

const CONNECTION_URI = `mongodb://${MONGODB_USERNAME}:${MONGODB_PASSWORD}@${MONG<PERSON>B_HOST}:${MONGODB_PORT}/${MONGODB_DATABASE}${MONGODB_CONNECTION_OPTIONS}`;

export function upsert(collection, query, object) {
    return co(function* () {
        return yield MongoClient.connect(CONNECTION_URI);
    }).then((client) => {
        let promise = client.db().collection(collection).update(query, object, {upsert: true});
        client.close();
        return promise;
    }).then((prom) => {
        EventStore.dispatch({type: 'CHANGED', collection: collection});
        return prom;
    }).catch((err) => {
        console.log(err);
    })
}

export function update(collection, query, object) {
    return co(function* () {
        return yield MongoClient.connect(CONNECTION_URI);
    }).then((client) => {
        let promise = client.db().collection(collection).updateMany(query, object);
        client.close();
        return promise;
    }).then((prom) => {
        EventStore.dispatch({type: 'CHANGED', collection: collection});
        return prom;
    }).catch((err) => {
        console.log(err);
    })
}

export function insert(collection, object) {
    return co(function* () {
        return yield MongoClient.connect(CONNECTION_URI);
    }).then((client) => {
        let promise = client.db().collection(collection).insert(object);
        client.close();
        return promise;
    }).then((prom) => {
        EventStore.dispatch({type: 'CHANGED', collection: collection});
        return prom;
    }).catch((err) => {
        console.log(err);
    })
}

export function count(collection, queryObject) {
    return co(function* () {
        return yield MongoClient.connect(CONNECTION_URI);
    }).then((client) => {
        let num = client.db().collection(collection).count(queryObject);
        client.close();
        return num;
    }).catch((err) => {
        console.log(err);
    })
}

export function findAll(collection, findQuery, sortQuery, limit) {
    return co(function* () {
        return yield MongoClient.connect(CONNECTION_URI);
    }).then((client) => {
        let list;
        if (limit) {
            list = client.db().collection(collection).find(findQuery).sort(sortQuery).limit(limit).toArray();
        }
        else {
            list = client.db().collection(collection).find(findQuery).sort(sortQuery).toArray();
        }
        client.close();
        return list;
    }).catch((err) => {
        console.log(err);
    })
}

export function find(collection, query) {
    return co(function* () {
        return yield MongoClient.connect(CONNECTION_URI);
    }).then((client) => {
        let item = client.db().collection(collection).findOne(query);
        client.close();
        return item;
    }).catch((err) => {
        console.log(err);
    })
}

export function findOneToMany(collection, query) {
    return co(function* () {
        return yield MongoClient.connect(CONNECTION_URI);
    }).then((client) => {
        let result = client.db().collection(collection).find(query).toArray();
        client.close();
        return result;
    }).catch((err) => {
        console.log(err);
    });
}


export function remove(collection, query) {
    return co(function* () {
        return yield MongoClient.connect(CONNECTION_URI);
    }).then((client) => {
        let item = client.db().collection(collection).remove(query);
        client.close();
        return item;
    }).catch((err) => {
        console.log(err);
    })
}
