import express from 'express';
import cors from 'cors';
import bodyParser from 'body-parser';
import http from 'http';
import apiRoutes from './api/api-routes';
import rtbRoutes from './api/rtb-routes';
import targetingApiRoutes from './targeting-api/routes';
import campaignManagementInventoryRoutes from './api/campaign-management-inventory-routes';
import campaignManagementBackendRoutes from "./api/campaign-management-backend-routes";
import adsApiRoutes from "./api/ads-api-routes";
import inventoryAggregatorRoutes from "./inventory-aggregator/routes";
import {initAdmin} from './admin/admin-routes';
import path from 'path';
import {createWss} from './services/websocket-server';
import "./init/initialise";
import adDeliveryDistributionConfigurationRoutes from "./api/ad-delivery-distribution-configuration-routes";
import tfpRoutes from "./api/tfp-routes";

// Constants
const PORT = process.env.PORT || 3001;

// App
const app = express();
app.use(bodyParser.json());

if (process.env.ENABLE_CORS === 'true') {
    console.log("Enabling cors")
    app.use(cors({
        origin: 'http://admin.lsd.test', optionsSuccessStatus: 200, credentials: true
    }));
}

const server = http.createServer(app);
createWss(server);

app.use('/api', apiRoutes);

app.use('/inventory-aggregator', inventoryAggregatorRoutes);

app.use('/rtb', rtbRoutes);

app.use('/targeting-api', targetingApiRoutes);

app.use('/campaign-management-inventory', campaignManagementInventoryRoutes)

app.use('/campaign-management-backend', campaignManagementBackendRoutes)

app.use('/tfp', tfpRoutes)

app.use('/ads-api', adsApiRoutes);

app.use("/ad-delivery-distribution-configuration/api", adDeliveryDistributionConfigurationRoutes)

// This is for the fast-sponsorship-schedule-manager TODO: find out why the routes are different
app.use('/campaign-management-inventory/api', campaignManagementBackendRoutes)

app.use(express.static(path.join(__dirname, '..', 'public')));
app.get('/', function (req, res) {
    res.sendFile(path.join(__dirname, '..', 'public', 'index.html'));
});
app.get('/version.json', function (req, res) {
    res.sendFile(path.join(__dirname, '../..', 'docker', 'version.json'));
});
app.get('/advert.js', function (req, res) {
    res.sendFile(path.join(__dirname, '..', 'public', 'ad', 'advert.js'));
});
app.get('/ad.css', function (req, res) {
    res.sendFile(path.join(__dirname, '..', 'public', 'ad', 'ad.css'));
});

//Mocks the core dsp tracking server.
const sendVastFile = function (req, res) {
    res.sendFile(path.join(__dirname, '..', 'public', 'vast', 'core-vast.xml'));
};
app.get('/other/vast', sendVastFile); // Currently used by bob to download the vast for core creatives.
app.get('/win/vast', sendVastFile); // Used by auctioneer as nurl for core creatives.
server.listen(PORT, '0.0.0.0', function listening() {
    console.log('Running on http://localhost:%d', server.address().port);
});

initAdmin();
