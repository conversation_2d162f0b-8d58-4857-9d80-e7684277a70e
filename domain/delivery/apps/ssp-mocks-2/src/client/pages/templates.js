import React from "react";
import {TemplateStore} from "../redux/template-store";
import {getTemplates} from "../services/api";

export class Templates extends React.Component {

    constructor(props) {
        super(props);
        this.state = {templates: [{_id: "no-templates", template: {response: "no templates found"}}]};
        this.onChanged = this.onChanged.bind(this);
        this.unsubscribe = TemplateStore.subscribe(this.onChanged);
        getTemplates();
    }

    onChanged() {
        this.setState({templates: TemplateStore.getState()});
    }

    render() {
        return (
                <div>
                    { this.state.templates.map((t) => {
                        return <Template key={t._id} response={t}/>
                    })}
                </div>
        )
    }
}

class Template extends React.Component {

    constructor(props) {
        super(props);
    }

    render() {
        return (<div className="container spacing">
                    <div className="card">
                        <header className="card-header">
                            <p className="card-header-title">
                                {this.props.response.template.name}
                            </p>
                        </header>
                        <div className="card-content partner-table">
                        <pre>
                            {JSON.stringify(this.props.response.template, null, 4)}
                        </pre>
                        </div>
                    </div>
                </div>)
    }

}
