import React from "react";
import {Requests} from "../components/requests";
import {Partners} from "../components/partners";
import {EditPartnerResponses} from "../components/edit-partner";

export class Home extends React.Component {

    render() {
        return (
            <div>
                <div className="columns">
                    <div className="column">
                        <Partners currentPreset={this.props.currentPreset} getPreset={this.props.getPreset}/>
                    </div>
                    <div className="column">
                        <EditPartnerResponses preset={this.props.currentPreset}/>
                    </div>
                </div>
                <div className="container spacing">
                    <Requests/>
                </div>
            </div>
        )
    }
}