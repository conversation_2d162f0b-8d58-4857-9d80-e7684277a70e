import React from "react";
import {Presets} from "../components/presets";
import {TemplateStore} from "../redux/template-store";
import {getTemplates} from "../services/api";

export class PresetPage extends React.Component {

    constructor(props) {
        super(props);
        this.getPreset = this.getPreset.bind(this);
        this.state = {presets: null};
    }

    getPreset(preset) {
        this.props.getPreset(preset);
        this.setState({preset: preset});
    }

    render() {
        return (
            <div>
                <div className="columns">
                    <div className="column">
                        <Presets getPreset={this.getPreset}/>
                    </div>
                </div>
            </div>
        )
    }
}