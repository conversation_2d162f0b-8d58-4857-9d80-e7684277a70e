html {
    height: 100%;
}

body {
    overflow: visible;
    height: inherit;
}

.clickable {
    cursor: pointer;
}

.side-nav {
    width: 220px;
}

.partner-table {
    padding: 20px;
}

.current-preset {
    text-align: right;
    height: 100%;
    margin: 0;
}

.preset-button {
    width: 8%;
}

.preset-name {
    width: 20%;
}

.card-title  {
    padding: 20px;
    color: white;
}

.card-header-title{
    width: 15%;
    margin-left: 20px;
}

.card-filter-search{
    padding: 20px;
    width: 75%;
}

.card-filter-title{
    padding-right: 10px;
    font-weight: bold;
}

.no-partner{
    width: 100%;
    padding-left: 0px;
    margin-left: 10px;
}

.component {
    padding: 20px
}

.spacing {
    margin-top: 20px;
}

.icon-draggable {
    text-align: center;
    padding-left: 17px;
    padding-top: 17px;
    position: absolute;
}

input{
    font-size: small;
    height: 30px;
    width: 610px;
}

.indicator {
    width: 110px;
}

i {
    margin-right: 5px;
}

td.min {
    width: 1%;
    white-space: nowrap;
}

i.no-margin {
    margin: 0;
}

@media screen and (min-width: 1408px) {
    .container {
        max-width: 1655px;
        width: auto;
    }
}

/*
REQUEST TABLE
*/
.table {
    table-layout:fixed;
    width: 100%;
    word-wrap: break-word;
}

.contains-table {
    display: table;
}

.req-url {
    width: 200px;
}

.req-timestamp {
    width: 200px;
}


/*
    Toastr configuration
 */

.toast {
    background-color: #1ab394;
    border-radius: 3px;
    padding: 1.25rem 2.5rem 1.25rem 1.5rem;
    position: relative;
}

#toast-container > .toast {
    background-image: none !important;
    padding: 15px 15px 15px 35px;
}

#toast-container > .toast:before {
    position: relative;
    font-family: FontAwesome;
    font-size: 24px;
    line-height: 18px;
    float: left;
    margin-left: -1em;
    color: #FFF;
    padding-right: 0.5em;
    margin-right: 0.5em;
}


.toast-success {
    background-color:  #1c84c6;
}
.toast-error {
    background-color:  #ec4758;
}
.toast-info {
    background-color:  #23c6c8;
}
.toast-warning {
    background-color:  #f7a54a;
}
