import React from "react";
import ReactDOM from "react-dom";
import "./services/websocket";
import {NavBar} from "./components/header";
import "bulma";
import "font-awesome/css/font-awesome.css";
import "toastr/toastr.scss";
import "./stylesheets/bulma-overrides.sass";
import "./stylesheets/mocks.css";
import {EventStore} from "./redux/event-store";
import {Pages} from "./constants/pages";
import {Templates} from "./pages/templates";
import {Home} from "./pages/home";
import {Events} from "./constants/events";
import {GamCampaigns} from "./pages/gam-campaigns";
import {GamAdUnits} from "./pages/gam-ad-units";
import {PresetPage} from "./pages/presets";

class App extends React.Component {

    constructor(props) {
        super(props);
        this.state = {page: Pages.HOME, currentPreset: null};
        this.switchPage = this.switchPage.bind(this);
        this.onPresetLoad = this.onPresetLoad.bind(this);
        this.unsubscribe = EventStore.subscribe(this.switchPage);
    }

    switchPage() {
        if (EventStore.getState().event === Events.PAGE) {
            this.setState({page: EventStore.getState().page});
        }
    }

    onPresetLoad(preset) {
        this.setState({currentPreset: preset});
    }

    getPage() {
        switch(this.state.page) {
            case Pages.TEMPLATES:
                return <Templates/>;
            case Pages.GAM_CAMPAIGNS:
                return <GamCampaigns />;
            case Pages.GAM_AD_UNITS:
                return <GamAdUnits />;
            case Pages.PRESETS:
                return <PresetPage getPreset={this.onPresetLoad}/>;
            default:
                return <Home currentPreset={this.state.currentPreset} getPreset={this.onPresetLoad}/>;
        }
    }

    componentWillUnmount() {
        this.unsubscribe();
    }

    render() {
        return <div>
            <NavBar currentPreset={this.state.currentPreset}/>
            <section>
                <div className="container spacing">
                    {this.getPage()}
                </div>
            </section>
        </div>;
    }
}


ReactDOM.render(<App />, document.getElementById('react'));
