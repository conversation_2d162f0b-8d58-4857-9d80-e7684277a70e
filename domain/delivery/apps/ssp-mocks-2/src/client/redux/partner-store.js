import { createStore } from 'redux'

export function getPartner(name) {
    let partners = PartnerStore.getState();
    let toReturn = {};
    partners.forEach((partner) => {
        if (partner.name === name) {
            toReturn = partner;
        }
    });
    return toReturn;
}

function store(state = {}, action) {
    switch (action.type) {
        case 'CHANGED':
            return action.partners;
        default:
            return state;
    }
}


export const PartnerStore = createStore(store);
