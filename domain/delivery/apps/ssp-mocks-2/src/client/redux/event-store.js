import { createStore } from 'redux'
import {Events} from '../constants/events';


function store(state = {}, action) {
    switch (action.type) {
        case Events.EDIT_PARTNER:
            return {event: action.type, data: action.data};
        case Events.RESPONSES_UPDATED:
            return {event: action.type};
        case Events.DELETE_PARTNER:
            return {event: action.type}
        case Events.PRETTY_JSON:
            return {event: action.type, request: action.request, view: action.view};
        case Events.PAGE:
            return {event: action.type, page: action.page};
        default:
            return state;
    }
}

export const EventStore = createStore(store);
