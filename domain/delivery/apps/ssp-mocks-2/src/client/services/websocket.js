import io from 'socket.io-client';
import {Collections} from '../constants/collections';
import {getRequests, getPartners} from './api';

let pathname = window.location.pathname.slice(0, -1) ;
export const ws = io.connect('', {path: pathname + '/socket.io/'});


ws.on('collection', (collection) => {
   switch (collection.name) {
       case Collections.PARTNERS:
           getPartners();
           break;
       case Collections.REQUESTS:
           getRequests();
           break;
   }
});