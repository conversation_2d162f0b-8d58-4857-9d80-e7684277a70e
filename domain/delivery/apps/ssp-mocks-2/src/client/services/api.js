import {RequestStore} from '../redux/request-store';
import {PartnerStore} from '../redux/partner-store';
import {GamCampaignsStore} from '../redux/gam-campaigns-store'
import {GamAdUnitsStore} from '../redux/gam-adunits-store'
import axios from 'axios';
import {TemplateStore} from "../redux/template-store";
import {Events} from "../constants/events";
import {EventStore} from "../redux/event-store";

axios.defaults.baseURL = window.location.pathname;

export function getRequests() {
    axios.get('/api/requests')
            .then((resp) => {
                RequestStore.dispatch({type: 'CHANGED', requests: resp.data});
            })
            .catch((err) => {
                console.error(err);
            })
}

export function deleteRequests() {
    return axios.delete('/api/requests')
            .then(() => {
                RequestStore.dispatch({type: 'CHANGED', requests: {}});
            })
            .catch((err) => {
                console.error(err);
            })
}

export function getPartners() {
    axios.get('/api/partners')
            .then((resp) => {
                PartnerStore.dispatch({type: 'CHANGED', partners: resp.data});
                EventStore.dispatch({type: Events.RESPONSES_UPDATED, data: {}});
            })
            .catch((err) => {
                console.error(err);
            })
}

export function getTemplates() {
    axios.get('/api/templates')
            .then((resp) => {
                TemplateStore.dispatch({type: 'CHANGED', templates: resp.data});
            })
            .catch((err) => {
                console.error(err);
            })
}

export function getGamCampaigns() {
    axios.get('/campaign-management-inventory/api/campaign/response-stub')
            .then((resp) => {
                GamCampaignsStore.dispatch({type: 'CHANGED', response: resp.data});
            })
            .catch((err) => {
                console.error(err);
            })
}

export function saveGamCampaigns(gamCampaigns) {
    axios.post('/campaign-management-inventory/api/campaign/response-stub', gamCampaigns)
            .catch((err) => console.error(err))
}

export function resetGamCampaigns() {
    axios.delete('/campaign-management-inventory/api/campaign/response-stub')
            .then((resp) => {
                GamCampaignsStore.dispatch({type: 'CHANGED', response: resp.data});
            })
            .catch((err) => console.error(err))
}

export function getGamAdUnits() {
    axios.get('/campaign-management-inventory/api/adunits/response-stub')
            .then((resp) => {
                GamAdUnitsStore.dispatch({type: 'CHANGED', response: resp.data});
            })
            .catch((err) => {
                console.error(err);
            })
}

export function saveGamAdUnits(gamAdUnits) {
    axios.post('/campaign-management-inventory/api/adunits/response-stub', gamAdUnits)
            .catch((err) => console.error(err))
}

export function resetGamAdUnits() {
    axios.delete('/campaign-management-inventory/api/adunits/response-stub')
            .then((resp) => {
                GamAdUnitsStore.dispatch({type: 'CHANGED', response: resp.data});
            })
            .catch((err) => console.error(err))
}

export function savePartner(partner, deletingResponse = false, responseName = null) {
    return axios.post('/api/partners', {partner: partner, deletingResponse: deletingResponse, responseName: responseName})
            .catch((err) => {
                console.error(err);
            })
}

export function resetPartner(body) {
    return axios.post('/api/reset', body)
            .catch((err) => {
                console.error(err);
            })
}

export function deletePartner(partner) {
    return axios.delete('/api/partner', {data: {partner: partner}})
            .catch((err) => {
                console.error(err);
            })
}

export function savePreset(preset) {
    return axios.put('/api/presets', {preset: preset})
            .catch((err) => console.error(err))
}

// Deletes all partners from mongodb then loads a preset with the given fileName and retrieves the new partners
export function loadPreset(fileName) {
    return axios.delete('/api/partners').then(() => {
        return axios.post(`/api/presets/${fileName}`).then(() => {
            return getPartners();
        }).catch((err) => {
            console.error(err);
        })
    }).catch((err) => {
        console.error(err);
    })
}

// Gets an Array with JSONObjects representing all presets stored in the repository
export function getPresets() {
    return axios.get('/api/presets').then((resp) => {
        return resp.data;
    }).catch((err) => {
        console.error(err);
    })
}

// Deletes the preset with the given fileName in the repository (~/init/presets)
export function deletePreset(fileName) {
    return axios.delete(`/api/presets/${fileName}`).then((resp) => {
        return resp;
    }).catch((err) => {
        console.error(err);
    })
}

// Gets the current environment that the app is running in
export function getEnvironment() {
    return axios.get('/api/environment').then((resp) => {
        return resp;
    }).catch((err) => {
        console.error(err);
    })
}
