import React from "react";
import AceEditor from "react-ace";
import toastr from "toastr";
import "brace/mode/json";
import "brace/mode/javascript";
import "brace/theme/github";
import {EventStore} from "../redux/event-store";
import {Events} from "../constants/events";
import {getPartner} from "../redux/partner-store";
import {savePartner, resetPartner} from "../services/api";
import $ from "jquery";
import {DEFAULT_PARTNER} from "../../server/constants/default-partner";
import {DragDropContext, Droppable, Draggable} from "react-beautiful-dnd";

class PartnerResponse extends React.Component {

    constructor(props) {
        super(props);
        this.state = this.props.state;
        this.state.response = this.props.response;
        this.state.index = this.props.index;
        this.state.collapsed = true;
        this.onChange = this.onChange.bind(this);
        this.confirm = this.confirm.bind(this);
        this.updateStatus = this.updateStatus.bind(this);
        this.toggleConditional = this.toggleConditional.bind(this);
        this.updateConditionalResponse = this.updateConditionalResponse.bind(this);
        this.togglePrebidServer = this.togglePrebidServer.bind(this);
        this.collapse = this.collapse.bind(this);
        this.delete = this.delete.bind(this);
        this.onEvent = this.onEvent.bind(this);
        this.unsubscribe = EventStore.subscribe(this.onEvent);
    }

    confirm() {
        let partner = this.props.partner;
        let index = this.props.findIndex(this.state.name, partner.responses);
        if (partner.name === undefined) {
            return;
        }
        try {
            if (partner.responses !== undefined) {
                partner.responses[index].response = JSON.parse(this.state.response);
            }
            else {
                partner.response = JSON.parse(this.state.response);
            }
        }
        catch (err) {
            toastr.error("Error saving response: " + err.message);
            return;
        }
        partner.responses[index].conditionalFunc = this.state.conditionalFunc;
        partner.responses[index].conditional = this.state.conditional;
        partner.responses[index].statusCode = this.state.statusCode;
        partner.responses[index].prebidServer = this.state.prebidServer;
        savePartner(partner)
                .then(() => {
                    toastr.info("Saved changes to partner: " + partner.name);
                });
    }

    componentWillUnmount() {
        this.props.partnerUnselected();
        this.unsubscribe();
    }

    onEvent() {
        let event = EventStore.getState();
        if (event.event === Events.RESPONSES_UPDATED || event.event === Events.EDIT_PARTNER) {
            this.forceUpdate();
        }
    }

    onChange(newValue) {
        this.setState({response: newValue});
    }

    updateConditionalResponse(newValue) {
        this.setState({conditionalFunc: newValue})
    }

    toggleConditional() {
        this.setState({conditional: !this.state.conditional})
    }

    togglePrebidServer() {
        this.setState({prebidServer: !this.state.prebidServer})
    }

    collapse() {
        if (this.state.collapsed) {
            this.setState({
                response: JSON.stringify(this.state.response, null, 4)
            })
        } else {
            this.setState({
                response: JSON.parse(this.state.response)
            })
        }
        this.setState({collapsed: !this.state.collapsed})
        this.props.responseExpanded(this.state.collapsed)
    }

    updateStatus(event) {
        this.setState({statusCode: event.target.value})
    }

    delete() {
        this.props.handleDelete(this.state.name);
    }

    render() {
        let response = this.state.response;
        let disabled = this.props.partner.name === undefined || this.state.collapsed;
        return (
            <div className="card">
            <header className="card-header clickable" onClick={this.collapse}>
                <i className="fa fa-ellipsis-v fa-lg icon-draggable"/>
                <p className="card-header-title">
                     {this.state.name}
                </p>
            </header>
            {disabled ? <div/> :
                <div className="card-content">
                <IsPrebidServerResponse enabled={this.state.prebidServer}
                                        onClick={this.togglePrebidServer}/>
                <hr/>
                <ConditionalResponse condFunc={this.state.conditionalFunc}
                                     enabled={this.state.conditional}
                                     onClick={this.toggleConditional}
                                     onChange={this.updateConditionalResponse}/>
                <hr/>
                <div>
                    <label htmlFor="statusCode">Status Code</label>
                    <input id="statusCode"
                           className="input"
                           type="number"
                           value={this.state.statusCode}
                           onChange={this.updateStatus}/>
                </div>
                <hr/>
                <AceEditor
                        mode="json"
                        theme="github"
                        onChange={this.onChange}
                        width="100%"
                        value={response}
                        name="edit-partner"
                        editorProps={{$blockScrolling: true}}
                />
                <footer className="card-footer">
                    <a className={"card-footer-item button is-primary"} disabled={disabled} onClick={this.confirm}>
                        <i className="fa fa-save"/>Save</a>
                    <a className={"card-footer-item button is-danger"} onClick={this.delete}><i className="fa fa-times"/>Delete</a>
                </footer>
            </div>}
        </div>)
    }
}

export class EditPartnerResponses extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            partner: "", clickable: false, resetCounter: 0, expandedResponses: 0,
        }

        this.onEvent = this.onEvent.bind(this);
        this.openNewModal = this.openNewModal.bind(this);
        this.openResetModal = this.openResetModal.bind(this);
        this.delete = this.delete.bind(this);
        this.findResponseIndex = this.findResponseIndex.bind(this);
        this.increaseResetCounter = this.increaseResetCounter.bind(this);
        this.onResponseUpdate = this.onResponseUpdate.bind(this);
        this.onDragEnd = this.onDragEnd.bind(this);
        this.responseExpanded = this.responseExpanded.bind(this);
        this.partnerUnselected = this.partnerUnselected.bind(this);
        this.unsubscribe = EventStore.subscribe(this.onEvent);

    }

    componentWillUnmount() {
        this.unsubscribe();
    }

    partnerUnselected() {
        this.setState({ expandedResponses: 0 });
    }

    responseExpanded(expanded) {
        let result = this.state.expandedResponses;
        if (expanded) {
            result += 1;
        } else {
            result -= 1;
        }
        this.setState( { expandedResponses : result } );

    }

    findResponseIndex(responseName, responses) {
        for (let index = 0; index < responses.length; index++) {
            if (responses[index].name === responseName) {
                return index;
            }
        }
        return null;
    }

    onResponseUpdate(partner) {
        this.setState({partner: partner});
    }

    delete(toDelete) {
        let index = this.findResponseIndex(toDelete, this.state.partner.responses);
        let partner = this.state.partner;
        try {
            if (partner.responses !== undefined) {
                let responses = partner.responses;
                responses.splice(index, 1);
                partner.responses = responses;
            }
            else {
                partner.responses = [];
            }
        }
        catch (err) {
            toastr.error("Error saving response: " + err.message);
            return;
        }
        savePartner(partner, true, toDelete)
                .then(() => {
                    toastr.info("Removed " + toDelete + " from " + partner.name);
                });
        EventStore.dispatch({type: Events.EDIT_PARTNER, data: {name: partner.name}});
    }

    onEvent() {
        let event = EventStore.getState();
        if (event.event === Events.EDIT_PARTNER) {
            let partner = getPartner(event.data.name);
            if (partner.responses !== undefined) {
                this.setState({
                    partner: partner, clickable: true
                })
            }
            else {
                let responses = [];
                responses.push({
                    "name": "Default Response",
                    "conditional": partner.conditional,
                    "conditionalFunc": partner.conditionalFunc,
                    "statusCode": partner.statusCode,
                    "response": partner.response,
                    "prebidServer": partner.prebidServer
                })
                partner.responses = responses;
                savePartner(partner)
                        .then(() => {
                            this.setState({
                                partner: partner, clickable: true
                            })
                        });
            }
        }
        if (event.event === Events.RESPONSES_UPDATED) {
            let partner = getPartner(this.state.partner.name);
            if (partner.responses !== undefined) {
                this.setState({
                    partner: partner, clickable: true
                })
            }
        }
        if (event.event === Events.DELETE_PARTNER) {
            this.setState({
                partner: "", clickable: false
            })
        }
    }

    openNewModal() {
        let clickable = this.state.clickable;
        if (clickable) {
            $('#responseModal').addClass('is-active');
        }
    }

    openResetModal() {
        let clickable = this.state.clickable;
        if (clickable) {
            $('#resetModal').addClass('is-active');
        }
    }

    increaseResetCounter() {
        this.setState({
            resetCounter: this.state.resetCounter++,
        })
    }

    onDragEnd(result) {
        if (!result.destination) {
            return;
        }
        let currentPos = result.source.index
        let newPos = result.destination.index
        if (currentPos === newPos) {
            return;
        }
        try {
            let partner = this.state.partner;
            partner.responses.splice(newPos, 0, partner.responses.splice(currentPos, 1)[0]);
            savePartner(partner)
                    .then(() => {
                        this.setState({partner: partner, responses: partner.responses})
                        toastr.info(`Responses for ${this.state.partner.name} have been re-ordered`);
                        EventStore.dispatch({type: Events.RESPONSES_UPDATED, data: {name: this.state.partner.name}});
                    });
        }
        catch (err) {
            toastr.error("Error changing order of responses: " + err.message);
        }
    }

    renderResponses() {
        if (this.state.partner.responses === undefined || this.state.partner.responses.length === 0) {
            if (this.state.clickable) {
                return (<p className="card-header-title no-partner" key={0}> This partner has no set responses </p>)
            }
            else {
                return (<p className="card-header-title no-partner" key={0}> Please select a partner </p>)
            }
        }
        else {
            return (
                <DragDropContext onDragEnd={this.onDragEnd}>
                    <Droppable droppableId="droppable">
                        {(provided) => (
                            <div {...provided.droppableProps}  ref={provided.innerRef}>
                            {this.state.partner.responses.map((response, index) => (
                                <Draggable key={response.name} draggableId={response.name} index={index}
                                           isDragDisabled={this.state.expandedResponses > 0}>
                                    {(provided) => (
                                        <div ref={provided.innerRef} {...provided.draggableProps} {...provided.dragHandleProps}>
                                            { <PartnerResponse
                                                    key={`${this.state.partner.name}${response.name}${this.state.resetCounter}`}
                                                    state={response}
                                                    response={response.response}
                                                    partner={this.state.partner}
                                                    handleDelete={this.delete}
                                                    findIndex={this.findResponseIndex}
                                                    responseExpanded={this.responseExpanded}
                                                    partnerUnselected={this.partnerUnselected}
                                                /> }
                                        </div>
                                    )}
                                </Draggable>
                                ))}
                                {provided.placeholder}
                            </div>
                        )}
                    </Droppable>
                </DragDropContext>
            )
        }
    }

    render() {
        return (<div className="card">
            <div className="card-header">
                <p className="card-header-title">
                    Edit {this.state.partner.name} Responses
                </p>
                <div className="card-header-icon">
                    <button className="button is-warning" disabled={!this.state.clickable} onClick={this.openResetModal}>
                        <i className="fa fa-refresh"/>Reset Responses
                    </button>
                </div>
            </div>
            <div className="card-content responses-list" key={this.state.partner.responses}>
                <ul>
                    {this.renderResponses()}
                </ul>
            </div>
            <footer className="card-footer">
                <a className={"card-footer-item button is-primary"} disabled={!this.state.clickable} onClick={this.openNewModal}>
                    <i className="fa fa-plus"/>Create New Response</a>
            </footer>

            <NewResponseModal partner={this.state.partner} handleResponseUpdate={this.onResponseUpdate}/>
            <ResetResponsesModal partner={this.state.partner} handleResetCount={this.increaseResetCounter} preset={this.props.preset}/>
        </div>)
    }
}

class ConditionalResponse extends React.Component {

    constructor(props) {
        super(props);
    }

    render() {
        return (<div>
            <div className="field is-grouped">
                <div className="control">
                    <div className="tags has-addons">
                        <span className="tag">Conditional Response</span>
                        <a className={this.props.enabled ? "tag is-primary" : "tag is-danger"}
                           onClick={this.props.onClick}>{this.props.enabled ? "Yes" : "No"}</a>
                    </div>
                </div>
            </div>
            {this.props.enabled ? <AceEditor
                    mode="javascript"
                    theme="github"
                    width="100%"
                    height="50px"
                    onChange={this.props.onChange}
                    value={this.props.condFunc}
                    name="conditional-response"
                    editorProps={{$blockScrolling: true}}
            /> : <div/>}
        </div>)
    }
}

class IsPrebidServerResponse extends React.Component {

    constructor(props) {
        super(props);
    }

    render() {
        return (<div>
            <div className="field is-grouped">
                <div className="control">
                    <div className="tags has-addons">
                        <span className="tag">Prebid Server Response</span>
                        <a className={this.props.enabled ? "tag is-primary" : "tag is-danger"}
                           onClick={this.props.onClick}>{this.props.enabled ? "Yes" : "No"}</a>
                    </div>
                </div>
            </div>
        </div>)
    }
}


class NewResponseModal extends React.Component {

    constructor(props) {
        super(props);
        this.state = {
            input: ""
        }
        this.updateField = this.updateField.bind(this)
        this.createResponse = this.createResponse.bind(this);
        this.closeModal = this.closeModal.bind(this);
    }

    updateField(input) {
        this.setState({input: input.target.value});
    }

    createResponse() {
        let partner = this.props.partner;
        let response = DEFAULT_PARTNER.responses[0];

        if (this.state.input.length === 0) {
            toastr.error("Response name can not be empty!");
            return;
        }
        if (partner.responses.map((response) => response.name).includes(this.state.input)) {
            toastr.error("Responses in the same partner must have unique names!");
            return;
        }

        response.name = this.state.input;
        partner.responses.push(response);
        savePartner(partner).then(() => {
            this.props.handleResponseUpdate(partner);
            this.setState({input: ""})
            this.closeModal();
            toastr.success("Created response: " + this.state.input);
        })
    }

    closeModal() {
        $('#responseModal').removeClass('is-active');
    }

    render() {
        return (<div id="responseModal" className="modal">
            <div className="modal-background"/>
            <div className="modal-card">
                <header className="modal-card-head">
                    <p className="modal-card-title">Create Response</p>
                </header>
                <section className="modal-card-body">
                    <div className="form-group">
                        <label>Response name</label>
                        <input id="responseName" className="input is-small" type="text" placeholder="E.g Response 11" aria-required="true"
                               value={this.state.input} onChange={this.updateField.bind(this)}/>
                    </div>
                </section>
                <footer className="modal-card-foot">
                    <a className="button is-primary" onClick={this.createResponse}><i className="fa fa-save"/>Save changes</a>
                    <a className="button" onClick={this.closeModal}><i className="fa fa-times"/>Cancel</a>
                </footer>
            </div>
        </div>);
    }
}

class ResetResponsesModal extends React.Component {

    constructor(props) {
        super(props);
        this.closeModal = this.closeModal.bind(this);
        this.confirm = this.confirm.bind(this);
    }

    confirm() {
        resetPartner({
            partner: this.props.partner,
            preset: this.props.preset,
        }).then(() => {
            toastr.success("Reset responses for: " + this.props.partner.name);
            this.props.handleResetCount();
            this.closeModal();
        });
    }

    closeModal() {
        $('#resetModal').removeClass('is-active');
    }

    render() {
        return (<div id="resetModal" className="modal">
            <div className="modal-background"/>
            <div className="modal-card">
                <header className="modal-card-head">
                    <p className="modal-card-title">Confirmation</p>
                </header>
                <section className="modal-card-body">
                    <p>Are you sure that you want to reset responses for {this.props.partner.name}?</p>
                    <p>{this.props.preset && this.props.preset.length !== 0 ? `This will reset the responses to the last saved state in the preset: ${this.props.preset.fileName}` : null}</p>
                </section>
                <footer className="modal-card-foot">
                    <a className="button is-warning" onClick={this.confirm}>Yes</a>
                    <a className="button" onClick={this.closeModal}>No</a>
                </footer>
            </div>
        </div>);
    }
}
