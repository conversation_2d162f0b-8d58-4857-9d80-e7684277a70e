import React from "react";
import {Pages} from "../constants/pages";
import {EventStore} from "../redux/event-store";
import {Events} from "../constants/events";
import {getEnvironment} from "../services/api";

export class NavBar extends React.Component {

    constructor(props) {
        super(props);
        this.gotoPage = this.gotoPage.bind(this);
        this.state = {environment : null};
        getEnvironment().then((resp) => {
            this.setState({environment : resp.data});
        })
    }

    gotoPage(e) {
        let page = e.target.name;
        EventStore.dispatch({type: Events.PAGE, page: page})
    }

    render() {
        return (<div key={this.props.currentPreset}>
            <nav className="navbar has-shadow is-spaced">
                <div className="container">
                    <div className="navbar-brand">
                        <h1 className="navbar-item title">
                            SSP Mocks
                        </h1>
                    </div>
                    <div className="navbar-menu">
                        <a name={Pages.HOME} className="navbar-item" onClick={this.gotoPage}>
                            <i className="fa fa-home"/>Home
                        </a>
                        <a name={Pages.TEMPLATES} className="navbar-item" onClick={this.gotoPage}>
                            <i className="fa fa-code"/>Templates
                        </a>
                        <a name={Pages.GAM_CAMPAIGNS} className="navbar-item" onClick={this.gotoPage}>
                            <i className="fa fa-book"/>Gam Campaigns
                        </a>
                        <a name={Pages.GAM_AD_UNITS} className="navbar-item" onClick={this.gotoPage}>
                            <i className="fa fa-book"/>Gam Ad Units
                        </a>
                        { this.state.environment === "dev" ?
                        <a name={Pages.PRESETS} className="navbar-item" onClick={this.gotoPage}>
                            <i className="fa fa-clipboard"/>Presets
                        </a> : null}
                    </div>
                    { this.state.environment === "dev" ?
                    <div>
                        <h1 className="navbar-item current-preset">
                            {
                                `Current preset: ${this.props.currentPreset ?
                                        this.props.currentPreset.fileName.slice(0, -5) : "None"}`
                            }
                        </h1>
                    </div> : null }
                </div>
            </nav>
        </div>)
    }
}

