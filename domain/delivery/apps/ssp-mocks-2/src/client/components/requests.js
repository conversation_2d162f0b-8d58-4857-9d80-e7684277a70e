import React from "react";
import toastr from "toastr";
import {RequestStore} from "../redux/request-store";
import {deleteRequests, getRequests} from "../services/api";
import {RequestType} from "../../server/constants/request-type";
import {EventStore} from "../redux/event-store";
import {Events} from "../constants/events";

const EMPTY_REQUEST = {request: "No requests received yet", response: "No requests received yet"};
const views = {REQUEST: 'request', RESPONSE: 'response'};

export class Requests extends React.Component {

    constructor(props) {
        super(props);
        this.state = {type: RequestType.BID, requests: [], filter: "", filteredRequests: [], selected: {}};
        this.onChanged = this.onChanged.bind(this);
        this.eventListener = this.eventListener.bind(this);
        this.onTypeChange = this.onTypeChange.bind(this);
        this.getTabClass = this.getTabClass.bind(this);
        this.filterRequests = this.filterRequests.bind(this);

        this.clearRequests = this.clearRequests.bind(this);
        this.unsubscribe = [];
        this.unsubscribe.push(RequestStore.subscribe(this.onChanged));
        this.unsubscribe.push(EventStore.subscribe(this.eventListener));
        getRequests();
    }

    onChanged() {
        this.setState({requests: RequestStore.getState()});
        this.filterRequests(this.state.filter);
    }

    eventListener() {
        if (EventStore.getState().event === Events.PRETTY_JSON) {
            $('#jsonModal').addClass('is-active');
            if (EventStore.getState().view === views.REQUEST) {
                this.setState({selected: {headers: EventStore.getState().request.headers, body: EventStore.getState().request.request}})
            } else {
                this.setState({selected: EventStore.getState().request.response})
            }
        }
    }


    onTypeChange(event) {
        this.setState({type: event.target.name});
        if(event.target.name === RequestType.BID) {
            getRequests();
        }
        else if (event.target.name === RequestType.NOTIFY) {
            // TODO implement notifications
        }
        else {
            throw Error("Request type not supported");
        }
    }

    getTabClass(type) {
        if (this.state.type === type) {
            return "is-active tab";
        }
        else {
            return "tab";
        }
    }

    clearRequests() {
        deleteRequests().then(() => {
            toastr.info("Requests Deleted!");
        });
    }

    updateFilter(filter) {
        this.setState({filter: filter.target.value});
        this.filterRequests(filter.target.value);
    }

    filterRequests(filter) {
        const requests = this.state.requests;
        let filtered = [];
        for (const request of requests) {
            if (JSON.stringify(request).includes(filter)) {
                filtered.push(request);
            }
        }
        this.setState({filteredRequests: filtered});
    }

    componentWillUnmount() {
        // Unsubscribe all event listeners
        this.unsubscribe.forEach(f => f());
    }

    render() {
        let requests = <Request key="no-req" request={EMPTY_REQUEST}/>;
        if (this.state.requests.length > 0) {
            requests = this.state.filteredRequests.map((req) => {
                return <Request key={req._id} request={req}/>;
            })
        }
        return (
                <div className="card">
                    <div className="card-header">
                        <div className="card-filter-search">
                            <label className="card-filter-title">Request Filter</label>
                            <form onSubmit={e => { e.preventDefault(); }}>
                            <input className="card-filter-input" type="text" placeholder="Filter Requests" onChange={this.updateFilter.bind(this)} />
                            </form>
                        </div>
                    </div>
                    <div className="card-header">
                        <div className="card-header-title">
                            Requests
                        </div>
                        <div className="card-header-icon">
                            <button className="button is-danger" onClick={this.clearRequests}>
                                <i className="fa fa-times"/> Clear Requests
                            </button>
                        </div>
                    </div>
                    <div className="card-content">
                        <table className="table">
                            <thead>
                            <tr>
                                <th className="req-timestamp">Timestamp</th>
                                <th className="req-url">URL</th>
                                <th>Body</th>
                            </tr>
                            </thead>
                            <tbody>
                            {requests}
                            </tbody>
                        </table>
                    </div>
                    <JsonModal json={this.state.selected}/>
            </div>
        )
    }
}


class Request extends React.Component {


    constructor(props) {
        super(props);
        this.state = {view: views.REQUEST};
        this.setView = this.setView.bind(this);
        this.isRequestView = this.isRequestView.bind(this);
        this.triggerModal = this.triggerModal.bind(this);
    }

    setView(event) {
        var name = event.target.name;
        this.setState({view: name});
    }

    isRequestView() {
        return this.state.view === views.REQUEST;
    }

    triggerModal() {
        EventStore.dispatch({type: Events.PRETTY_JSON, request: this.props.request, view: this.state.view})
    }

    render() {
        let content = this.isRequestView() ? this.props.request.request : this.props.request.response;
        let reqClass = this.isRequestView()  ? "tab is-active" : "tab";
        let rspClass = this.isRequestView()  ? "tab" : "tab is-active";
        return <tr className="hoverable">
            <td>{this.props.request.timestamp}</td>
            <td>{this.props.request.url}</td>
            <td>
                <div className="tabs">
                    <ul >
                        <li className={reqClass} onClick={this.setView}><a className="clickable" name={views.REQUEST}>Request</a></li>
                        <li className={rspClass} onClick={this.setView}><a className="clickable" name={views.RESPONSE}>Response</a></li>
                    </ul>
                </div>
                <p className="clickable" onClick={this.triggerModal}>
                    {JSON.stringify(content)}
                </p>
            </td>
        </tr>
    }
}

class JsonModal extends React.Component {
    constructor(props) {
        super(props);
        this.hide = this.hide.bind(this);
    }

    hide() {
        $('#jsonModal').removeClass('is-active');
    }

    render() {
        return (<div id="jsonModal" className="modal">
            <div className="modal-background" onClick={this.hide}/>
            <div className="modal-card">
                <section className="modal-card-body">
                    <div className="highlight">
                        <pre>
                            {JSON.stringify(this.props.json, null, 2)}
                        </pre>
                    </div>
                </section>
            </div>
        </div>);
    }
}


