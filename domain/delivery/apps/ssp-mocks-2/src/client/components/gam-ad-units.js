import React from "react";
import AceEditor from "react-ace";
import toastr from "toastr";
import "brace/mode/json";
import "brace/mode/javascript";
import "brace/theme/github";
import {GamAdUnitsStore} from "../redux/gam-adunits-store";
import {saveGamAdUnits, getGamAdUnits, resetGamAdUnits} from "../services/api";

export class EditGamAdUnits extends React.Component {

    constructor(props) {
        super(props);
        this.state = {
            response: "[]", statusCode: 200, delayMs: 0
        };
        this.confirm = this.confirm.bind(this);
        this.onEvent = this.onEvent.bind(this);
        this.onChange = this.onChange.bind(this);
        this.updateStatus = this.updateStatus.bind(this);
        this.updateDelay = this.updateDelay.bind(this);
        this.unsubscribe = GamAdUnitsStore.subscribe(this.onEvent);
        getGamAdUnits()
    }

    onEvent() {
        let resp = GamAdUnitsStore.getState();
        this.setState({response: resp.response, statusCode: resp.statusCode, delayMs: resp.delayMs});
    }

    onChange(newValue) {
        this.setState({response: newValue});
    }

    confirm() {
        saveGamAdUnits({response: this.state.response, statusCode: this.state.statusCode, delayMs: this.state.delayMs});
        toastr.info("Saved changes to gam ad units");
    }

    reset() {
        resetGamAdUnits();
        toastr.info("Reset Gam ad units done.");
    }

    componentWillUnmount() {
        this.unsubscribe();
    }

    updateStatus(event) {
        this.setState({statusCode: event.target.value ? parseInt(event.target.value) : ""});
    }

    updateDelay(event) {
        this.setState({delayMs: event.target.value ? parseInt(event.target.value) : ""});
    }

    render() {
        let response = this.state.response;
        return (<div className="card">
            <header className="card-header clickable" onClick={this.collapse}>
                <p className="card-header-title">
                    Edit Gam Ad Units Response
                </p>
            </header>
            <div className="card-content">
                <div>
                    <label htmlFor="delayMs">Delay millis</label>
                    <input id="delayMs"
                           className="input"
                           type="number"
                           value={this.state.delayMs}
                           onChange={this.updateDelay}/>
                </div>
                <hr/>
                <div>
                    <label htmlFor="statusCode">Status Code</label>
                    <input id="statusCode"
                           className="input"
                           type="number"
                           value={this.state.statusCode}
                           onChange={this.updateStatus}/>
                </div>
                <hr/>
                <AceEditor
                        mode="json"
                        theme="github"
                        onChange={this.onChange}
                        width="100%"
                        value={response}
                        name="edit-partner"
                        editorProps={{$blockScrolling: true}}
                />
            </div>
            <footer className="card-footer">
                <a className={"card-footer-item button is-primary"} onClick={this.confirm}><i className="fa fa-save"/>Save</a>
                <a className={"card-footer-item button"} onClick={this.reset}><i className="fa fa-reset"/>Reset</a>
            </footer>
        </div>)
    }
}
