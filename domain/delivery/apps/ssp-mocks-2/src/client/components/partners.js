import React from "react";
import Select from "react-select";
import {PartnerStore} from "../redux/partner-store";
import {EventStore} from "../redux/event-store";
import {Events} from "../constants/events";
import {ILLEGAL_CHARACTERS} from "../../server/constants/illegal-characters";
import {getPartners, savePartner, deletePartner, savePreset, getPresets, getEnvironment} from "../services/api";
import $ from "jquery";
import toastr from "toastr";
import {DEFAULT_PRESET_FILE_NAME, DEFAULT_PARTNER} from "../../server/constants/default-partner";

export class Partners extends React.Component {

    constructor(props) {
        super(props);
        this.state = {partners: [], currentPreset: null, showConfirm: false, deleting: {}, environment: null};
        this.onChanged = this.onChanged.bind(this);
        this.unsubscribe = PartnerStore.subscribe(this.onChanged);
        this.openModal = this.openModal.bind(this);
        this.onDeletingPartner = this.onDeletingPartner.bind(this);
        this.openNewPresetModal = this.openNewPresetModal.bind(this);
        this.openReplacePresetModal = this.openReplacePresetModal.bind(this);
        this.onPresetSaved = this.onPresetSaved.bind(this);
        getEnvironment().then((resp) => {
            this.setState({environment: resp.data});
        })
        getPartners();
    }

    onPresetSaved(preset) {
        this.props.getPreset(preset)
    }

    onChanged() {
        this.setState({partners: PartnerStore.getState()});
    }

    onDeletingPartner(toDelete) {
        this.setState({showConfirm: true, deleting: toDelete});
        $('#confirmModal').addClass('is-active');
    }

    componentWillUnmount() {
        this.unsubscribe();
    }

    openModal() {
        this.setState({existing: this.state.partners.map((p) => p.name)});
        $('#modal').addClass('is-active');
    }

    openNewPresetModal() {
        getPresets().then((presets) => {
            this.setState({
                currentPreset: this.props.currentPreset,
                allPresets: presets,
            });
            $('#createPresetModal').addClass('is-active');
        })
    }

    openReplacePresetModal() {
        getPresets().then((presets) => {
            this.setState({
                currentPreset: this.props.currentPreset,
                allPresets: presets,
            });
            // If 1 or fewer presets exist then there are no presets that can be overwritten
            if (this.state.allPresets && this.state.allPresets.length <= 1) {
                toastr.error("No presets exist that can be overwritten (default-preset is immutable)");
                return;
            }
            $('#replacePresetModal').addClass('is-active');
        })
    }

    render() {
        return (<div className="card">
                    {this.state.environment === "dev" ? <header className="card-header">
                        <p className="card-header-title">
                            Presets
                        </p>
                        <div className="card-header-icon">
                            <button className="button is-primary" onClick={this.openNewPresetModal}><i className="fa fa-floppy-o"/>Save new Preset</button>
                        </div>
                        <div className="card-header-icon">
                            <button className="button is-primary" onClick={this.openReplacePresetModal}><i className="fa fa-floppy-o"/>Save over existing
                                Preset
                            </button>
                        </div>
                    </header> : null}
                    <header className="card-header">
                        <p className="card-header-title">
                            Partners
                        </p>
                        <div className="card-header-icon">
                            <button className="button is-primary" onClick={this.openModal}><i className="fa fa-plus"/>Create New Partner</button>
                        </div>
                    </header>
                    <div className="card-content partner-table">
                        <table className="content table bordered">
                            <thead>
                            <tr>
                                <th>Name</th>
                                <th>Endpoint</th>
                                <th>Requests</th>
                                <th>Notifications</th>
                                <th/>
                            </tr>
                            </thead>

                            <tbody>
                            {
                                this.state.partners ?
                                this.state.partners.sort(function (a, b) {
                                    return a.name.toUpperCase() < b.name.toUpperCase() ? -1 : 1;
                                }).map((p) => {
                                    return <Partner key={p._id} partner={p} handler={this.onDeletingPartner}/>
                                })
                                : null
                            }
                            </tbody>
                        </table>
                    </div>

                    <PartnerCreationModal existing={this.state.existing}/>
                    <PresetCreationModal allPresets={this.state.allPresets} partners={this.state.partners} onPresetSaved={this.onPresetSaved}/>
                    <PresetReplaceModal allPresets={this.state.allPresets} currentPreset={this.props.currentPreset}/>
                    <ConfirmationModal visible={this.state.showConfirm} partner={this.state.deleting}/>
                </div>)
    }
}

class Partner extends React.Component {

    constructor(props) {
        super(props);
        this.state = {selected: false};
        this.onClick = this.onClick.bind(this);
        this.onChange = this.onChange.bind(this);
        this.onDelete = this.onDelete.bind(this);
        this.unsubscribe = EventStore.subscribe(this.onChange);
    }

    onChange() {
        let event = EventStore.getState();
        if (event.event === Events.EDIT_PARTNER) {
            let isCurrent = event.data.name === this.props.partner.name;
            this.setState({selected: isCurrent});
        }
    }

    onDelete() {
        this.props.handler(this.props.partner);
    }

    onClick() {
        this.setState({selected: true});
        EventStore.dispatch({type: Events.EDIT_PARTNER, data: {name: this.props.partner.name}})
    }

    componentWillUnmount() {
        this.unsubscribe();
    }

    render() {
        let css = this.state.selected ? "orange lighten-5 " : "";
        let endPoint = this.props.partner.endpoint.replace("__HOST__DOMAIN__", window.location.hostname);
        endPoint = endPoint.replace(":__PORT__", ":" + window.location.port);
        return (<tr onClick={this.onClick} className={"clickable hoverable " + css}>
            <td>{this.props.partner.name}</td>
            <td><span>{endPoint}</span></td>
            <td>{this.props.partner.requests}</td>
            <td>{this.props.partner.notifications ? this.props.partner.notifications : 0}</td>
            <td className={"min"}>
                <button className="button is-danger" onClick={this.onDelete}><i className={"fa fa-times no-margin"}/></button>
            </td>
        </tr>)
    }
}

class PresetCreationModal extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            preset: {
                fileName: "",
                description: "",
                partners: []
            },
            allPresets: [],
        };
        this.updateFileName = this.updateFileName.bind(this);
        this.updateDescription = this.updateDescription.bind(this);
        this.createPreset = this.createPreset.bind(this);
        this.closeModal = this.closeModal.bind(this);
        this.state.allPresets = this.props.allPresets;
    }

    componentDidUpdate(prevProps, prevState, snapshot) {
        if (prevProps.allPresets !== this.props.allPresets) {
            this.setState({allPresets: this.props.allPresets});
        }
    }

    updateFileName(event) {
        let preset = this.state.preset;
        preset.fileName = event.target.value;
    }

    updateDescription(event) {
        let preset = this.state.preset;
        preset.description = event.target.value;
    }

    createPreset() {
        let preset = this.state.preset;
        let fileName = preset.fileName;

        if (fileName.length === 0) {
            toastr.error("Preset file name can not be empty!");
            return;
        }
        // replace spaces with hyphens
        fileName = fileName.replace(/\s+/g, '-');
        for (let i = 0; i < ILLEGAL_CHARACTERS.length; i++) {
            if (fileName.includes(ILLEGAL_CHARACTERS[i])) {
                toastr.error(`Preset file name includes illegal character! (\'${ILLEGAL_CHARACTERS[i]}\')`);
                return;
            }
        }
        if (fileName.slice(-5) !== ".json") {
            preset.fileName = fileName + ".json";
        }
        if (this.state.allPresets.map((preset) => preset.fileName).includes(preset.fileName)) {
            toastr.error(`A preset already exists with the File Name: ${preset.fileName}.\n
                        Use the other button to save over an existing preset.`);
            return;
        }

        preset.partners = PartnerStore.getState();
        this.setState({preset: preset});
        savePreset(this.state.preset).then(() => {
            this.props.onPresetSaved(this.state.preset);
            this.closeModal();
            toastr.success("Created preset: " + this.state.preset.fileName);
        });
    }

    closeModal() {
        $('#createPresetModal').removeClass('is-active');
    }

    render() {
        return (<div id="createPresetModal" className="modal">
            <div className="modal-background"/>
            <div className="modal-card">
                <header className="modal-card-head">
                    <p className="modal-card-title">Create Preset</p>
                </header>
                <section className="modal-card-body">
                    <div className="form-group">
                        <label>File Name</label>
                        <input id="presetFileName" className="input is-small" type="text" placeholder="E.g default-preset-12" aria-required="true"
                               onChange={this.updateFileName}/>
                        <label>Description</label>
                        <input id="presetDescription" className="input is-small is-expanded" type="text" placeholder="..." aria-required="true"
                               onChange={this.updateDescription}/>
                    </div>
                </section>
                <footer className="modal-card-foot">
                    <a className="button is-primary" onClick={this.createPreset}><i className="fa fa-save"/>Save changes</a>
                    <a className="button" onClick={this.closeModal}><i className="fa fa-times"/>Cancel</a>
                </footer>
            </div>
        </div>);
    }
}

class PresetReplaceModal extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            preset: {
                fileName: "",
                description: "",
                partners: []
            },
            allPresets: [],
            currentPreset: [],
        };
        this.updateDescription = this.updateDescription.bind(this);
        this.createPreset = this.createPreset.bind(this);
        this.closeModal = this.closeModal.bind(this);

        this.state.allPresets = this.props.allPresets;
        let currentPreset = this.props.currentPreset;
        if (currentPreset && currentPreset.length !== 0) {
            this.state.currentPreset = currentPreset;
            this.state.preset.fileName = currentPreset.fileName;
            this.state.preset.description = currentPreset.description;
        }

    }

    componentDidUpdate(prevProps, prevState, snapshot) {
        if (prevProps.allPresets !== this.props.allPresets) {
            let presetsWithLabels = this.props.allPresets;
            for (let i = 0; i < presetsWithLabels.length; i++) {
                presetsWithLabels[i].label = `${presetsWithLabels[i].fileName}`;
            }
            this.setState({allPresets: presetsWithLabels});
        }
    }

    updateDescription(event) {
        let preset = this.state.preset;
        preset.description = event.target.value;
        this.setState({preset: preset})
    }

    createPreset() {
        let preset = this.state.preset;

        if (preset.fileName.length === 0) {
            toastr.error("Preset file name can not be empty.");
            return;
        }
        if (preset.fileName === DEFAULT_PRESET_FILE_NAME) {
            toastr.error("The default preset can not be overwritten.");
            return;
        }

        preset.partners = PartnerStore.getState();
        this.setState({preset: preset});
        savePreset(this.state.preset).then(() => {
            this.closeModal();
            toastr.success("Updated preset: " + this.state.preset.fileName);
        });
    }


    closeModal() {
        $('#replacePresetModal').removeClass('is-active');
    }

    render() {
        return (<div id="replacePresetModal" className="modal" key={this.props.allPresets}>
            <div className="modal-background"/>
            <div className="modal-card">
                <header className="modal-card-head">
                    <p className="modal-card-title">Overwrite Preset</p>
                </header>
                <section className="modal-card-head">
                    <div className="container">
                        <div className="row">
                            <div className="col-md-4"></div>
                            <div className="col-md-4">
                                Existing Presets:
                                { this.state.allPresets ?
                                <Select options={this.state.allPresets.filter(preset => preset.fileName !== DEFAULT_PRESET_FILE_NAME)}
                                        onChange={(values) => this.setState({preset: values})}/>
                                : <Select options={this.state.allPresets}
                                          onChange={(values) => this.setState({preset: values})}/>
                                }
                            </div>
                            <div className="col-md-4"></div>
                        </div>
                    </div>
                </section>

                <section className="modal-card-body">
                    <div className="form-group">
                        <label>File Name</label>
                        <input id="presetFileName" className="input is-small" readOnly={true} type="text" value={this.state.preset.fileName} aria-required="true"
                               onChange={this.updateFileName}
                        />
                        <label>Description</label>
                        <input id="presetDescription" className="input is-small is-expanded" type="text" value={this.state.preset.description} aria-required="true"
                               onChange={this.updateDescription}/>
                    </div>
                </section>
                <footer className="modal-card-foot">
                    <a className="button is-primary" onClick={this.createPreset}><i className="fa fa-save"/>Save changes</a>
                    <a className="button" onClick={this.closeModal}><i className="fa fa-times"/>Cancel</a>
                </footer>
            </div>
        </div>);
    }
}

class PartnerCreationModal extends React.Component {
    constructor(props) {
        super(props);
        this.state = {partner: {}};
        this.updateField = this.updateField.bind(this);
        this.createPartner = this.createPartner.bind(this);
        this.closeModal = this.closeModal.bind(this);
    }

    updateField(event) {
        let partner = this.state.partner;
        let value = event.target.value;
        let model = value === "NOT_SET" ? null : value;
        partner.name = model;
        partner.endpoint = "http://__HOST__DOMAIN__:__PORT__/rtb/" + model + "/bid";
        partner.requests = 0;
        partner.statusCode = 200;
        partner.response = {};
    }

    createPartner() {
        if (this.state.partner.name.length === 0) {
            toastr.error("Partner name can not be empty!");
            return;
        }
        if (this.state.partner.name.includes('~')) {
            toastr.error("Partner name includes illegal character! (\'~\')");
            return;
        }
        let context = this;
        if (this.props.existing.includes(this.state.partner.name)) {
            context.closeModal();
            toastr.error("Partner name " + context.state.partner.name + " already exists! Could not create partner!");
        }
        else {
            this.state.partner.responses = [DEFAULT_PARTNER.responses[0]]
            savePartner(this.state.partner).then(() => {
                context.closeModal();
                toastr.success("Created partner: " + context.state.partner.name);
            });
        }
    }

    closeModal() {
        $('#modal').removeClass('is-active');
    }

    render() {
        return (<div id="modal" className="modal">
            <div className="modal-background"/>
            <div className="modal-card">
                <header className="modal-card-head">
                    <p className="modal-card-title">Create Partner</p>
                </header>
                <section className="modal-card-body">
                    <div className="form-group">
                        <label>Partner name</label>
                        <input id="partnerName" className="input is-small" type="text" placeholder="E.g Appnexus" aria-required="true"
                               onChange={this.updateField}/>
                    </div>
                </section>
                <footer className="modal-card-foot">
                    <a className="button is-primary" onClick={this.createPartner}><i className="fa fa-save"/>Save changes</a>
                    <a className="button" onClick={this.closeModal}><i className="fa fa-times"/>Cancel</a>
                </footer>
            </div>
        </div>);
    }
}


class ConfirmationModal extends React.Component {
    constructor(props) {
        super(props);
        this.hide = this.hide.bind(this);
        this.deletePartner = this.deletePartner.bind(this);
    }

    hide() {
        $('#confirmModal').removeClass('is-active');
    }

    deletePartner() {
        let partner = this.props.partner;
        deletePartner(partner).then(() => {
            toastr.success("Deleted partner: " + partner.name);
            EventStore.dispatch({type: Events.DELETE_PARTNER});
        });
        this.hide();
    }

    render() {
        return (<div id="confirmModal" className="modal">
            <div className="modal-background"/>
            <div className="modal-card">
                <header className="modal-card-head">
                    <p className="modal-card-title">Confirmation</p>
                </header>
                <section className="modal-card-body">
                    <p>Are you sure you want to delete partner {this.props.partner.name}?</p>
                </section>
                <footer className="modal-card-foot">
                    <a className="button is-danger" onClick={this.deletePartner}>Yes</a>
                    <a className="button" onClick={this.hide}>No</a>
                </footer>
            </div>
        </div>);
    }
}
