import React from "react";
import {getPresets, deletePreset, loadPreset} from "../services/api";
import $ from "jquery";
import toastr from "toastr";
import {DEFAULT_PRESET_FILE_NAME} from "../../server/constants/default-partner";
import {EventStore} from "../redux/event-store";
import {Events} from "../constants/events";
import {Pages as Page} from "../constants/pages";

export class Presets extends React.Component {

    constructor(props) {
        super(props);
        this.state = {presets: [], showConfirm: false, deleting: {}};
        this.onDeletionConfirmed = this.onDeletionConfirmed.bind(this);
        this.openModal = this.openModal.bind(this);
        this.onDeletingPreset = this.onDeletingPreset.bind(this);
        this.openConfirmModal = this.openConfirmModal.bind(this);
        this.onLoadingPreset = this.onLoadingPreset.bind(this);
        getPresets().then((presets) => {
            this.state.presets = presets;
            this.forceUpdate();
        })
    }

    onDeletionConfirmed(fileName) {
        this.setState({
            presets: this.state.presets.filter(function (preset) {
                return preset.fileName !== fileName;
            })
        });
        this.props.getPreset(null)
    }

    onDeletingPreset(toDelete) {
        this.setState({showConfirm: true, deleting: toDelete});
        this.openConfirmModal();
    }

    onLoadingPreset(preset) {
        loadPreset(preset.fileName).then(() => {
            this.props.getPreset(preset);
            toastr.success("Loaded preset from " + preset.fileName);
            // Go back to the Home screen
            EventStore.dispatch({type: Events.PAGE, page: Page.HOME});
        })
    }

    openConfirmModal() {
        $('#confirmModal').addClass('is-active');
    }

    openModal() {
        this.setState({existing: this.state.presets.map((p) => p.fileName)});
        $('#modal').addClass('is-active');
    }

    render() {
        return (<div className="card">
                    <header className="card-header">
                        <p className="card-header-title">
                            Presets
                        </p>
                    </header>
                    <div className="card-content partner-table">
                        <table className="content table bordered">
                            <thead>
                            <tr>
                                <th className="preset-name">File Name</th>
                                <th>Description</th>
                                <th className="preset-button"/>
                                <th className="preset-button"/>
                            </tr>
                            </thead>

                            <tbody>
                            { this.state.presets !== [] && this.state.presets ?
                                this.state.presets.sort(function (a, b) {
                                    return a.fileName.toUpperCase() < b.fileName.toUpperCase() ? -1 : 1;
                                }).map((preset) => {
                                    return <Preset key={preset.fileName} preset={preset} handler={this.onDeletingPreset} loadPreset={this.onLoadingPreset}/>
                                }) : null}
                            </tbody>
                        </table>
                    </div>
                    <ConfirmationModal visible={this.state.showConfirm} preset={this.state.deleting} updateState={this.onDeletionConfirmed}/>
                </div>)
    }
}

class Preset extends React.Component {

    constructor(props) {
        super(props);
        this.state = {selected: false};
        this.onClick = this.onClick.bind(this);
        this.onDelete = this.onDelete.bind(this);
        this.onLoad = this.onLoad.bind(this);
    }

    // Called on Delete Preset button being pressed, calls onDeletingPreset in the Presets parent
    // onDeletingPreset opens the ConfirmationModel where the actual deleting happens
    onDelete() {
        this.props.handler(this.props.preset);
    }

    onLoad() {
        this.props.loadPreset(this.props.preset);
    }

    onClick() {
        this.setState({selected: true});
    }

    render() {
        let css = this.state.selected ? "orange lighten-5 " : "";
        return (<tr onClick={this.onClick} className={"clickable hoverable " + css}>
            <td>{this.props.preset.fileName}</td>
            <td>{this.props.preset.description}</td>
            <td className={"min"}>
                {
                    this.props.preset.fileName === DEFAULT_PRESET_FILE_NAME ? null :
                    <button className="button is-danger" onClick={this.onDelete}><i className={"fa fa-times no-margin"}/></button>
                }
            </td>
            <td className={"min"}>
                <button className="button is-success" onClick={this.onLoad}><i className="fa fa-download no-margin"/></button>
            </td>
        </tr>)
    }
}

class ConfirmationModal extends React.Component {
    constructor(props) {
        super(props);
        this.hide = this.hide.bind(this);
        this.deletePreset = this.deletePreset.bind(this);
        this.updateState = this.updateState.bind(this);
    }

    hide() {
        $('#confirmModal').removeClass('is-active');
    }

    updateState(fileName) {
        this.props.updateState(fileName);
    }

    deletePreset() {
        let fileName = this.props.preset.fileName;
        deletePreset(fileName).then((resp) => {
            if (resp.status === 200) {
                this.updateState(fileName);
                toastr.success("Deleted preset: " + this.props.preset.fileName);
            }
        })
        this.hide();
    }

    render() {
        return (<div id="confirmModal" className="modal">
            <div className="modal-background"/>
            <div className="modal-card">
                <header className="modal-card-head">
                    <p className="modal-card-title">Confirmation</p>
                </header>
                <section className="modal-card-body">
                    <p>Are you sure you want to delete preset: {this.props.preset.fileName}?</p>
                </section>
                <footer className="modal-card-foot">
                    <a className="button is-danger" onClick={this.deletePreset}>Yes</a>
                    <a className="button" onClick={this.hide}>No</a>
                </footer>
            </div>
        </div>);
    }
}
