FROM node:17-alpine

# Install node and yarn
RUN apk update && apk add bash && yarn global add webpack@3.12.0 -g


ENV APP_ROOT=/app

RUN mkdir -p ${APP_ROOT}/logs
WORKDIR ${APP_ROOT}

# Install app dependencies
COPY package.json yarn.lock ${APP_ROOT}/
RUN yarn install

COPY docker/files/entrypoint.sh ${APP_ROOT}/

# Bundle app source
COPY . ${APP_ROOT}

RUN webpack

ENV HOST=localhost:3001
ENV NODE_ENV=production
ENV PRESET_DIRECTORY=/usr/src/app/presets


CMD [ "/bin/bash", "/app/entrypoint.sh" ]
