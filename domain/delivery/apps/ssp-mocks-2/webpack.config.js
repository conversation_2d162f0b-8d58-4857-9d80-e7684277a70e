const path = require('path');
const webpack = require('webpack');

let client = {
    mode: 'development',
    entry: './src/client/app.js', output: {
        path: path.resolve(__dirname, 'src', 'public', 'built'), filename: 'bundle.js'
    }, target: 'web', module: {
        rules: [{test: /\.(js|jsx)$/, use: 'babel-loader', exclude: /node_modules/}, {
            test: /\.css/, use: [{
                loader: "style-loader" // creates style nodes from JS strings
            }, {
                loader: "css-loader" // translates CSS into CommonJS
            }]
        }, {
            test: /\.(ttf|otf|eot|svg|woff(2)?)(\?[a-z0-9]+)?$/,
            loader: 'file-loader',
            options: {
                name: '[name].[ext]', outputPath: '../fonts/'
            }

        }, {
            test: /\.sass$/, use: [{
                loader: "style-loader" // creates style nodes from JS strings
            }, {
                loader: "css-loader" // translates CSS into CommonJS
            }, {
                loader: "sass-loader" // compiles Sass to CSS
            }]

        }, {
            test: /\.scss$/, use: [{
                loader: "style-loader" // creates style nodes from JS strings
            }, {
                loader: "css-loader" // translates CSS into CommonJS
            }, {
                loader: "sass-loader" // compiles Sass to CSS
            }]

        }]
    }, plugins: [new webpack.DefinePlugin({
    })]
};

module.exports = [client];