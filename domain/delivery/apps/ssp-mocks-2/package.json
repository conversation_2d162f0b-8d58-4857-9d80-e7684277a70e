{"name": "ssp-mocks", "version": "1.0.0", "description": "", "main": "src/server/server.js", "scripts": {"build": "webpack", "analyse": "source-map-explorer src/public/built/bundle.js", "watch": "npm-run-all --parallel watch:server watch:build", "watch:build": "webpack --watch", "watch:server": "nodemon --exec 'npm run start' --watch \"./src/server\"", "start": "babel-node src/server/server.js", "local": "MONGODB_HOST=localhost PRESET_DIRECTORY=${LABS_DEV_ROOT}/ssp-core/domain/delivery/apps/ssp-mocks-2/src/server/init/presets yarn run watch"}, "author": "", "license": "ISC", "dependencies": {"axios": "^0.24.0", "body-parser": "^1.19.1", "brace": "^0.11.1", "bulma": "^0.9.3", "co": "^4.6.0", "cors": "^2.8.5", "express": "^4.17.1", "font-awesome": "^4.7.0", "jquery": "^3.6.0", "mongodb": "^3.7.3", "react": "^17.0.2", "react-ace": "^5.10.0", "react-beautiful-dnd": "^13.1.0", "react-dom": "^17.0.2", "react-router-dom": "^4.3.1", "react-scripts": "^5.0.1", "react-select": "^5.3.2", "redux": "^3.7.2", "sass": "^1.45.0", "socket.io": "^4.4.0", "socket.io-client": "^4.4.0", "toastr": "^2.1.4", "uuid": "^8.3.2", "winston": "^3.3.3"}, "devDependencies": {"@babel/cli": "^7.16.0", "@babel/core": "^7.16.5", "@babel/node": "^7.16.5", "@babel/plugin-proposal-class-properties": "^7.16.5", "@babel/plugin-proposal-decorators": "^7.16.5", "@babel/plugin-proposal-export-namespace-from": "^7.16.5", "@babel/plugin-proposal-function-sent": "^7.16.5", "@babel/plugin-proposal-json-strings": "^7.16.5", "@babel/plugin-proposal-numeric-separator": "^7.16.5", "@babel/plugin-proposal-throw-expressions": "^7.16.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/polyfill": "^7.12.1", "@babel/preset-env": "^7.16.5", "@babel/preset-es2015": "^7.0.0-beta.53", "@babel/preset-react": "^7.16.5", "babel-loader": "^8.2.3", "css-loader": "^0.28.11", "file-loader": "^4.3.0", "happypack": "^4.0.1", "nodemon": "^1.19.4", "npm-run-all": "^4.1.5", "sass-loader": "^12.4.0", "source-map-explorer": "^2.5.2", "style-loader": "^0.23.1", "url-loader": "^0.6.2", "webpack": "^5.65.0", "webpack-cli": "^4.9.1"}}