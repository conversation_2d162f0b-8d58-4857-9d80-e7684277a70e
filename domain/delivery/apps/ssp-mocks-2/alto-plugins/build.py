import os
from os.path import join, dirname, abspath

from alto.plugins import Plugin
from alto.util import run

DIR = dirname(__file__)
project_dir = dirname(DIR)


class Other(Plugin):

    def unit_test(self, apps, incremental, quiet) -> bool:
        self.build(apps)
        old = os.getcwd()
        os.chdir(project_dir)
        try:
            run(f'docker build -f Dockerfile -t test:mocks {project_dir}')
        except RuntimeError:
            return False
        finally:
            os.chdir(old)

        return True


def init(config):
    return Other(config, 'mocks')
