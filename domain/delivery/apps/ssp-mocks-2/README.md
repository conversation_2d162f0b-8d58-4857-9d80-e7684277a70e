# Mocks2

Mocks2 is a tool for testing the delivery chain by acting as mock DSPs' with configured responses. 

## Table of Contents
- [How to run](#how-to-run)
- [Presets](#presets)
- [Related Apps](#related-apps)

## How to Run

### Using yarn
- lsd add mongo && lsd up mongo
- yarn install
- yarn run build
- yarn run local (runs on http://localhost:3001)

### Using lsd:
- lsd add mocks2 && lsd up mocks2
- go to http://mocks2.lsd.test/ (also runs on http://localhost:3001)

## Presets

Presets are a snapshot of the current state of the partners and responses in the mocks2 app. They are stored in [ssp-mocks-2/src/server/init/presets](../ssp-mocks-2/src/server/init/presets) in json format. 

Because presets are stored in a directory, to truly persist them they need to be pushed into the repository. 
It also means that their management is environment dependent, and a specific command is required to pull them when they're saved in a csd environment. Presets are not available in production environments.

### Saving Presets on a personal device (yarn or local lsd)

Because of the power of volumes, regardless of whether you're running mocks2 through lsd or yarn,
the presets will be saved as json files in the aforementioned directory. 
If you think a preset that you've saved would be useful to you or others, then you should push it to the repository and merge it into master.

### Saving Presets in csd

Because they are stored as files in a directory, presets that are "saved" from CSD are actually stored in the directory of some container in a massive machine in Sydney... not ideal. 

To pull the saved presets from the directory in your CSD environment into your local environment, 
use the command: `csd sync-presets`.

This command will replace identically named presets in your local environment with the ones from CSD
and copy all uniquely named files. To make your presets' folder a strict copy of the one in your CSD 
environment, use the '-d' flag `csd sync-presets -d`

If you think these presets would be useful to you or others, then you should push them to the repository.

## Related Apps

[KanyeTest](../../../development-toolbox/apps/kanyetest) is the primary tool for querying the partners configured in mocks2

