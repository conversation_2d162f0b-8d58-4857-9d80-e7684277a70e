#!/usr/bin/env bash

JAVA_OPTS='-server'
JAVA_OPTS+=" -XX:InitialRAMPercentage=${JVM_INITIAL_RAM_PERCENTAGE:-80.0}"
JAVA_OPTS+=" -XX:MaxRAMPercentage=${JVM_MAX_RAM_PERCENTAGE:-80.0}"
JAVA_OPTS+=" -XX:MinRAMPercentage=${JVM_MIN_RAM_PERCENTAGE:-80.0}"
JAVA_OPTS+=' -Dfile.encoding=UTF8'
JAVA_OPTS+=' -XX:+UseG1GC'
JAVA_OPTS+=' -XX:+UseStringDeduplication'
JAVA_OPTS+=' -Djava.awt.headless=true'

if [[ "${DEBUG}" == 'true' ]]; then
    echo 'Debugging container...'
    JAVA_OPTS+=" -Xrunjdwp:server=y,transport=dt_socket,address=*:13261,suspend=n "

    # kotlin coroutine opts
    JAVA_OPTS+=" -Dkotlinx.coroutines.debug "
fi

echo "\$JAVA_OPTS set to: \"${JAVA_OPTS}\""

env | sort

exec java ${JAVA_OPTS} -Dspring.profiles.active="${CONFIG_ENVIRONMENT_NAME}" -jar prebid-cache.jar