FROM public.ecr.aws/docker/library/maven:3.9.3-amazoncorretto-17 AS build

RUN mkdir source && \
    yum install -y git && \
    git clone https://github.com/mbrtargeting/prebid-cache-java.git source

WORKDIR source

RUN git fetch && git checkout stroeer
RUN mvn dependency:resolve -T1.0C
RUN mvn dependency:resolve-plugins -T1.0C

RUN rm src/main/resources/*.yml && rm src/main/resources/*.xml
COPY conf/application.yml src/main/resources/
COPY conf/log4j2-console.xml src/main/resources/
#TODO: Replace the prod log4j
RUN mvn package -Dmaven.test.skip

FROM public.ecr.aws/amazoncorretto/amazoncorretto:17

ENV APP_DIR="/app"

RUN mkdir -p ${APP_DIR}/bin && \
    chown -R nobody:nobody ${APP_DIR}

COPY --from=build --chown=nobody:nobody source/target/prebid-cache.jar ${APP_DIR}
COPY --chown=nobody:nobody docker/run.sh ${APP_DIR}/bin/
COPY --chown=nobody:nobody docker/version.json ${APP_DIR}/
RUN chmod +x ${APP_DIR}/bin/run.sh

USER nobody
WORKDIR ${APP_DIR}

ARG STROEER_VERSION=UNKNOWN
ENV STROEER_VERSION=${STROEER_VERSION}

CMD [ "bin/run.sh" ]