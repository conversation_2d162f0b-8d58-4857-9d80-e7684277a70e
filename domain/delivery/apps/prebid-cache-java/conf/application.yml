info.app.name: @project.name@
info.app.artifact: @project.artifactId@
info.app.description: @project.description@
info.app.version: @project.version@
spring.main.banner-mode: "off"
api.path: /cache
server.port: 10511
server.compression.enabled: true
#server.compression.min-response-size: 1
#server.compression.mime-types: application/json,application/xml

# cors
cors:
  enabled: false
  mapping: "/cache/**"
  allowedOrigins: "*"
  allowedMethods: GET,POST
  allowCredentials: true

# cache
cache:
  prefix: prebid_
  expiry_sec: 300
  timeout_ms: 300
  clients_cache_duration: 86400
  clients_cache_size: 1000
  host_param_protocol: https

# logging
logging.level.root: info
logging.config: classpath:log4j2-console.xml

# metrics
management:
  graphite:
    metrics:
      export:
        enabled: false
        host: localhost
        port: 17364
        protocol: plaintext
        step: 1m
        tags-as-prefix:
          - prefix
        prefix: prebid

# circuit breaker
circuitbreaker:
  failure_rate_threshold: 50
  open_state_duration: 60000
  closed_state_calls_number: 5
  half_open_state_calls_number: 3

# endpoint actuators
management.server.port: 20305
management.health.defaults.enabled: false
management.endpoints.enabled-by-default: false
management.endpoints.web.base-path: /
management.health.diskspace.enabled: false
management.health.redis.enabled: false
management.endpoints.web.exposure.include: info, health, metrics
management.endpoint.info.enabled: true
management.endpoint.health.enabled: true
management.endpoint.metrics.enabled: true
management.endpoint.env.enabled: false
management.endpoint.configprops.enabled: false
management.endpoint.health.show-details: always
management.endpoint.shutdown.enabled: false
management.endpoint.configprops.keys-to-sanitize: password,secret,key,token,.*credentials.*,vcap_services
management.endpoint.info.cache.time-to-live: 5s
management.endpoint.health.cache.time-to-live: 5s
management.endpoint.metrics.cache.time-to-live: 5s
management.endpoint.env.cache.time-to-live: 5s
management.endpoint.configprops.cache.time-to-live: 5s

# undertow
server.undertow.buffer-size: 16384
server.undertow.direct-buffers: true

---
spring.config.activate.on-profile: local
cache.expiry_sec: 300
cache:
  min_expiry: 15
  max_expiry: 28800
  allow_external_UUID: true
  secondary_cache_path: "cache"

management:
  graphite:
    metrics:
      export:
        enabled: false
spring:
  redis:
    host: localhost
    port: 6379
    password: password
    timeout: 1000

---
# dev
spring.config.activate.on-profile: dev
logging.level.root: debug
logging.config: classpath:log4j2-console.xml

cache:
  min_expiry: 15
  max_expiry: 28800
  expiry_sec: 28800
  allow_external_UUID: true

spring:
  redis:
    host: redis.service.ssp.consul
    port: 6379
    password: password
    timeout: 1000

---
# prod
spring.config.activate.on-profile: eu-central-1
management.endpoint.metrics.enabled: false
management.endpoint.env.enabled: false
management.endpoint.configprops.enabled: false
logging.level.root: warn
logging.config: classpath:log4j2-console.xml

spring:
  redis:
    host: prebid-cache-redis.private.adscale.de
    port: 6379
    timeout: 1000

cache:
  min_expiry: 15
  max_expiry: 28800
  expiry_sec: 28800
  allow_external_UUID: true