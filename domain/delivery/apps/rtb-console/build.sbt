name := "RTB-Console"
organization := "org.adscale"
import java.util.Properties
import scala.xml.Elem
import java.net.URL

val appProperties = settingKey[Properties]("The application properties")

appProperties := {
    val prop = new Properties()
    IO.load(prop, new File("conf/application.conf"))
    prop
}

version := appProperties.value.getProperty("app.version")

scalaVersion := "2.11.7"

Keys.javaOptions+="-Dconfig.file=conf/application.conf"

crossPaths := false

val centralMirrorId = "labs-maven"
val mavenSettingsFile = Path.userHome / ".m2" / "settings.xml"
val settingsXml = mavenSettingsFile.asFile match {
    case mvn if mvn.canRead => Some(xml.XML.loadFile(mavenSettingsFile))
    case _ => None
}

resolvers := {
    extractMirrorUrlById(settingsXml, centralMirrorId) match {
        case Some(mirrorUrl) => {
            println(s"Found mirror url $mirrorUrl for id $centralMirrorId")
            Seq("central" at mirrorUrl)
        }
        case None => {
            println(s"No mirror found for id $centralMirrorId in settings.xml")
            Seq.empty
        }
    }
}

credentials ++= addCredentialsById(settingsXml, centralMirrorId)
credentials ++= addCredentialsById(settingsXml, "ssp-release-maven") // For publishing to ssp-release in website module

lazy val website = (project in file("modules/website")).enablePlugins(PlayScala)

lazy val root = (project in file(".")).enablePlugins(PlayScala).dependsOn(website).aggregate(website)

routesGenerator := InjectedRoutesGenerator

sources in (Compile, doc) := Seq.empty

publishArtifact in (Compile, packageDoc) := false

maintainer := "Adscale Labs Ltd."

dockerExposedPorts := Seq(9000, 9443)

dockerExposedVolumes := Seq("/opt/docker/logs")


def extractMirrorUrlById(settingsXml: Option[Elem], mirrorId: String): Option[String] = {
    settingsXml match {
        case Some(xml) => (xml \ "mirrors" \ "mirror")
                .find(mirror => (mirror \ "id").text == mirrorId)
                .map(mirror => (mirror \ "url").text)
        case None => None
    }
}

def addCredentialsById(settingsXml: Option[Elem], mirrorId: String): Seq[Credentials] = {
    extractMirrorUrlById(settingsXml, mirrorId) match {
        case Some(mirrorUrl) => {
            extractServerById(settingsXml, mirrorId) match {
                case Some(server) => {
                    println(s"Credentials found for $mirrorUrl")
                    Seq(Credentials("Artifactory Realm", new URL(mirrorUrl).getHost, (server \ "username").text, (server \ "password").text))
                }
                case None => Seq.empty
            }
        }
        case None => Seq.empty
    }
}

def extractServerById(settingsXml: Option[Elem], serverId: String): Option[scala.xml.Node] = {
    settingsXml match {
        case Some(xml) => (xml \ "servers" \ "server")
                .find(server => (server \ "id").text == serverId)
        case None => None
    }
}
