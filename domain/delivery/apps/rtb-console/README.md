# RTB-Console

The rtb-console is an app that is responsible for managing our demand and supply partner configurations. New partners are inserted directly into the database with some scripts, rtbconsole doesn't deal with creation.

## How to develop the rtb-console

### machine set up
Some pre-requisites you will need: [dev-setup](/docs/office/dev-setup.md), and additionally use [sdkman](https://sdkman.io/) or your preferred method to install [sbt](https://www.scala-sbt.org/). If you are going to use `lsd` to test locally with docker compose and you're on linux you should also look at [linux setup](/office/linux.md) as we rely on dnsmasq to route the lsd.test hostname to docker.

Getting it imported into IDEA properly is a bit of a debacle, the play binaries don't seem to be hosted any more. I'd say build and test it on the command line and just use IDEA to edit. See [<PERSON>'s instructions](#phils-installation) if you want to have a crack at running it from IDEA.

### Set java version through sdkman
`sdk env` to switch your sdk to the one this project uses (You might need to do `sdk env install` to install the java8 sdk if you are missing it)

### snapshot
To snapshot run with [alto](/bin/alto): `alto snapshot rtbconsole`. This updates the versions in the right files so that alto knows to build the app.

### build
- To build the docker image: `alto build rtbconsole`
- If you get an error about dependencies not found then you might need to temporarily remove the mbr artifactory from your settings file (`~.m2/settings.xml`)

### run in a local test environment
To run the docker container locally for testing `lsd clean && lsd add -d rtbconsole && lsd up`, this will set up a docker compose file containing all the test dependencies, then build and start your containers. All going well it should be available at http://rtbconsole.lsd.test/rtb-console for testing. You can login on lsd with the credentials user:`<EMAIL>`, pass: `password`.

### run the test suite
`lsd clean && lsd add flyway-db && lsd db empty && lsd up` then `alto unit_test rtbconsole`. The database is required for testing. This changes your local testing database flavour to `empty` so you will need to run `lsd db dishwasher` to change back to our typical test database which is populated with mock partners.

### release
Run `release` and fill in the details it asks for, it should be an alias [from ssp-default-profile](https://github.com/mbrtargeting/ssp-default-profile/blob/5d98b6883da7b1e88d1b37b2c7ef4928cdeacce2/aliases#L41). This enqueues the release job in jenkins. Notifications about the status of the job are pushed to #ssp-release-dev in slack. After it's merged and docker containers pushed to the registry it will automatically roll out on our k8s cluster and should be available at https://rtb-console.tools.adscale.de/rtb-console/

## PHIL'S INSTALLATION
 Install IntelliJ Scala plugin (has Play support). This plugin sucks at importing existing Play project.
 Download and install Scala 2.10.3. Download and install Play 2.2.2
 Forget activator - we are using older versions here... so...
 So use the Play command tool to generate idea project. 'play' command, inside play terminal execute 'idea'. See https://www.playframework.com/documentation/2.1.x/IDE
 Have IntelliJ import this idea project. IntelliJ detects its an old version and will upgrade it.
 Can now run the App via IDE. Not sure if this is the best way but it gets the job done.

## Setting up Grafana for `lsd` / `csd`

* Please refer to the `PCS` `README.md`
