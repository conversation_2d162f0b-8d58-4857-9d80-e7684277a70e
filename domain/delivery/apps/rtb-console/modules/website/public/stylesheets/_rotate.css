/* Just some base styles not needed for example to function */
*,html {
	font-family: Verdana, Arial, Helvetica, sans-serif;
	font-size: 12px
}

body,form,ul,li,p,h1,h2,h3,h4,h5 {
	margin: 0;
	padding: 0;
}

body {
	background-color: #606061;
	color: #ffffff;
}

img {
	border: none;
}

p {
	font-size: 1em;
	margin: 0 0 1em 0;
}

table {
	border-style: solid;
	border: thin;
	border-color: black;
}

th {
	background-color: grey;
	color: white;
	height: 270px;
	padding-bottom: 3px;
	padding-left: 1px;
	padding-right: 1px;
	text-align: center;
	vertical-align: bottom;
	padding-right: 5px;
}

th.monit {
	background-color: #FF99CC;
}

th.partner {
	background-color: #6600FF;
}

th.vertica {
	background-color: #CC66FF;
}

th.internal {
	background-color: #006633;
}

td {
	padding-left: 10px;
	padding-right: 1px;
	color: black;
}

td.partner {
	background-color: #99CCFF;
}

td.partnerName {
	font-weight: bold;
}

td.number {
	text-align: right;
}

td.numberMonit {
	text-align: right;
	background-color: #FFCCCC;
}

td.numberInternal {
	text-align: right;
	background-color: #99CC99;
}

td.numberVertica {
	text-align: right;
	background-color: #CCCCFF;
}

td.internal {
	background-color: #99CC99;
}

tr {
	background-color: #FFFF99;
}