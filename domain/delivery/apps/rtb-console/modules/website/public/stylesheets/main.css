body {
	font-family: sans-serif;
	font-size: 12px;
	margin: 0;
	padding: 0;
	background-color: #F8F8F8;
}

div.body {
	width: 1300px;
	margin: 0 auto;	
}

div.container {
    width: 1280px;
}

.centered
{
    text-align:center;
}

#header {
	background-position: 0 90px;
	height: 90px;
    line-height:90px;
}

#logo {
    position: relative;
    top: 12px;
    height:45px;
    width: auto;
}

#caption {
	float: right;
    font-family: Verdana,Arial,Helvetica,sans-serif;
    font-size: 36px;
    font-weight: bold;
    padding-bottom: 10px;
    padding-left: 40px;
    padding-top: 25px;
}

ul#navigation {
    float: left;
    position: relative;
    width: 1300px;
}

div.navigation {
    border-bottom: 1px solid #DFDFDF;
    border-top: 1px solid #DFDFDF;
	text-align: center;
	padding-top: 10px;
	height: 20px;
}

.menuItem {
	font-size: 12px;
	text-align: center;
	vertical-align: middle;
	padding-left: 10px;
	padding-right: 20px;
	float: left;
	color: black;
}

a:link, a:visited, a:hover, a:active {
	color: black;
	text-decoration: none;
}

fieldset {
	border: 0px;
}

div.content {
	margin: 20px 5px 5px;
}

div.partner {
	font-size: 18px;
	padding-bottom: 10px;
}

div.button {
	width: 800px;
	text-align: center;
	padding: 10px;
}

div.leftBorder {
	width: 800px; 
	float: left;
	border-left-width: 1px;
	border-left-color: #D0D0D0;
	border-left-style: solid;
	padding-left: 20px;
}

div.footer {
	border-top-width: 1px;
    border-top-color: #D0D0D0;
    border-top-style: solid;
    padding-top: 10px;
}

p.error {
    color: Red;
}

table.naked {
	border-width: 0px;
    border-style: none;
    
}

table.naked td {
    padding-top: 5px;
}

table, tr {
 	border-color: #D0D0D0;
	border-collapse: collapse;
}

th {
	color: #404040;
	background-color: #E8E8E8;
	padding-top: 5px;
	padding-bottom: 5px;
}

table.partner {
    width: 1250px;
}

table.partner tr {
	border-width: 1px;
    border-style: solid;
    border-color: #D0D0D0;
    border-collapse: collapse;
}

table.partner th {
    height: 280px;
    padding-bottom: 3px;
    padding-left: 1px; 
    padding-right : 5px;
    text-align: center;
    vertical-align: bottom;
    padding-right: 5px;
    color: #404040;
}

th.partner {
    background-color: #E8E8E8;
}

th.internal {
    background-color: #D0D0D0;
}

th.monit {
    background-color: #E8E8E8;
}

th.vertica {
    background-color: #D0D0D0;
}


table a:link, table a:visited, table a:hover, table a:active {
    color: #6699FF;
    text-decoration: none;
}

table.partner td {
  padding-top: 6px; 
  padding-bottom: 6px; 
  padding-left: 10px; 
  padding-right : 5px;
  color: #484848;
}


td.number {
    text-align: right;
}

td.numberPartner {
    text-align: right;
    background-color: #99CCFF;
}

td.numberMonit {
    text-align: right;
    background-color: #FFCCCC;
}

td.internal {
    background-color: #99CC99;
}

td.numberInternal {
    text-align: right;
    background-color: #99CC99;
}

td.numberVertica {
    text-align: right;
    background-color: #CCCCFF;
}

td.partner {
    background-color: #99CCFF;
}

table.partner td.number.numberMonit {
    text-align: right;
    background-color: #FFCCCC;
}

td {
	padding:3px;
	color: black;
}

table.detail, td.detail {
	border-collapse: collapse;
	border: 0px;
    word-wrap:break-word;
}

td.bold {
	font-weight: bold;
	border: 0px;
}

td.bold-editable {
	font-weight: bold;
	border: 0px;
	color: forestgreen;
}

.verticalText {
    text-align: center;
    vertical-align: middle;
    width: 20px;
    margin: 0px;
    padding: 0px;
    padding-left: 3px;
    padding-right: 3px;
    padding-top: 10px;
    white-space: nowrap;
    -webkit-transform: rotate(-90deg);
    -moz-transform: rotate(-90deg);
}

.tooltip{
    display: inline;
    position: relative;
}

.tooltip:hover:after{
    background: dodgerblue;
    border-radius: 5px;
    bottom: 26px;
    color: #fff;
    content: attr(title);
    left: 20%;
    padding: 5px 15px;
    position: absolute;
    z-index: 98;
    width: 220px;
}

.tooltip:hover:before{
    border: solid;
    border-color: dodgerblue transparent;
    border-width: 6px 6px 0 6px;
    bottom: 20px;
    content: "";
    left: 50%;
    position: absolute;
    z-index: 99;
}

.sortorder:after {
    content: '\25b2';
}
.sortorder.reverse:after {
    content: '\25bc';
}

.blacklisted-formats {
    list-style: none;
    padding-left: 0px;
}
#whitelistSelect, #blacklistSelect, #format-whitelistSelect, #format-blacklistSelect{
    width: 100%;
    height: 300px;
    list-style: none;
}

#whitelistContainer, #blacklistContainer, #format-whitelistContainer, #format-blacklistContainer{
    display: inline-block;
    width: 40%;
    height: 300px;
}

#filterBlacklist, #filterWhitelist {
    margin-bottom: 5px;
}

#buttons {
    display: inline-block;
    vertical-align: top;
    margin-top: 125px;
}

#buttons div{
    display: block;
    margin-bottom: 5px;
}

#blacklistPicker {
    height: 375px; 
}

.error-message {
    color: red;
    margin: 0 0 10px;
}

.boolean {
    display: inline-block;
    height: 16px;
    width: 16px;
    background-size: 16px 16px;
    background-repeat: no-repeat no-repeat;
}
