$(document).ready(function() {

    //global variables

    var whitelistSelect = $('#whitelistSelect');
    var blacklistSelect = $('#blacklistSelect');
    var whitelistButton = $('#whitelistButton');
    var blacklistButton = $('#blacklistButton');
    var allBlacklistButton = $('#allBlacklistButton');
    var allWhitelistButton = $('#allWhitelistButton');
    var hiddenBlacklistInput = $('#blacklistInput'); //a hidden input field holds comma separated blacklisted format from server
    var hiddenWhitelistInput = $('#whitelistInput'); //a hidden input field holds comma separated non blacklisted format from server
    var filterWhitelistInput = $("#filterWhitelist");
    var filterBlacklistInput = $("#filterBlacklist");

    var blacklist = listFromCommaSeparatedInput(hiddenBlacklistInput);
    var whitelist = listFromCommaSeparatedInput(hiddenWhitelistInput);

    function listFromCommaSeparatedInput(input) {
        return input.val().split(",").filter(String);
    }

    function populateSelect(list, select) {
        for (var i = 0; i < list.length; i++) {
            var option = $("<option>");
            var format = list[i];
            option.attr('value', format);
            option.text(format);
            option.attr('id', 'opt-' + format);
            select.append(option);
        }
    }

    function moveSelectedBetweenSelects(from, isBlacklisting) {
        var selected = from.val();
        if (selected && isBlacklisting) {
            blacklist = $(blacklist).not(selected).get();
            Array.prototype.push.apply(whitelist, selected);
        } else if (selected && !isBlacklisting) {
            whitelist = $(whitelist).not(selected).get();
            Array.prototype.push.apply(blacklist, selected);
        }
        hiddenBlacklistInput.val(blacklist.join(','));
        hiddenWhitelistInput.val(whitelist.join(','));
    }

    function redraw() {
        blacklistSelect.empty();
        whitelistSelect.empty();
        populateSelect(blacklist, blacklistSelect);
        populateSelect(whitelist, whitelistSelect);
    }

    function getFilteredFormats(select) {
        var filteredFormats = [];
        select.find("option:visible").each(function() {
            filteredFormats.push($(this).text());
        });
        return filteredFormats;
    }

    function addAllToList(isBlackListing) {

        var filteredBlacklist = getFilteredFormats(blacklistSelect);
        var filteredWhitelist = getFilteredFormats(whitelistSelect);

        if (isBlackListing) {
            Array.prototype.push.apply(blacklist, filteredWhitelist);
            whitelist = whitelist.filter(function(format) {
                return filteredWhitelist.indexOf(format) < 0;
            });
        } else {
            Array.prototype.push.apply(whitelist, filteredBlacklist);
            blacklist = blacklist.filter(function(format) {
                return filteredBlacklist.indexOf(format) < 0;
            });
        }
        hiddenBlacklistInput.val(blacklist.join(','));
        hiddenWhitelistInput.val(whitelist.join(','));
    }

    function filter_select(elementId, keyword) {
        // filters out <option> that don't start with keyword from target select
        var select = document.getElementById(elementId);
        var firstChild = '';
        for (var i = 0; i < select.length; i++) {
            var txt = select.options[i].text;
            var include = txt.toLowerCase().startsWith(keyword.toLowerCase());
            if (include && !firstChild) {
                firstChild = select.options[i].text;
            }
            select.options[i].style.display = include ? 'list-item' : 'none';
        }
        select.value = firstChild;
    }

    function clear_filter() {
        filterWhitelistInput.val('');
        filterBlacklistInput.val('');

    }

    whitelistSelect.bind("keydown", function(e) {
        if(e.which == 13) {
            blacklistButton.click();
            e.preventDefault();
        }
    });

    blacklistSelect.bind("keydown", function(e) {
        if(e.which == 13) {
            whitelistButton.click();
            e.preventDefault();
        }
    });

    filterWhitelistInput.bind("keydown", function(e) {
        if(e.which == 13) {
            blacklistButton.click();
            e.preventDefault();
        }
    });
    filterBlacklistInput.bind("keydown", function(e) {
        if(e.which == 13) {
            whitelistButton.click();
            e.preventDefault();
        }

    });

    filterWhitelistInput.bind("keyup", function(e) {
        if(e.which == 40) {
            whitelistSelect.focus();
        }

        filter_select("whitelistSelect", $(this).val())
    });

    filterBlacklistInput.bind("keyup",function(e) {
        if(e.which == 40) {
            blacklistSelect.focus();
        }
        filter_select("blacklistSelect", $(this).val())
    });

    whitelistButton.click(function() {
        moveSelectedBetweenSelects(blacklistSelect, true);
        redraw();
        clear_filter();

    });

    blacklistButton.click(function() {
        moveSelectedBetweenSelects(whitelistSelect, false);
        redraw();
        clear_filter();

    });

    allBlacklistButton.click(function() {
        addAllToList(true);
        redraw();
        clear_filter();
    });

    allWhitelistButton.click(function() {
        addAllToList(false);
        redraw();
        clear_filter();
    });

    redraw();
});