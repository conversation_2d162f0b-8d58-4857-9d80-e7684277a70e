'use strict';

var consoleApp = angular.module('angconsole', ['dndLists']);

consoleApp.controller("IvtController", function($scope, $http, $attrs) {
    $scope.sspIvt = { ivtEnable: false, ivtPercentage: 0};

    $scope.save = function() {
        var json = angular.toJson($scope.sspIvt);
        console.log('saving ...' + json);
        var request = $http({
            url : '/rtb-console/ssp/editIvtConfig/'+$attrs.partnerid,
            method : "POST",
            data : json,
            transformRequest : false,
            headers : {
                'Content-Type' : 'application/json'
            }
        }).success(function(data, status, headers, config) {
            $scope.movieResponse = data;
            if (data.validation == true) {
                $scope.changeRoute(data.redirectPath);
            } else {
                $scope.status = data;
            }
        }).error(function(data, status, headers, config) {
            $scope.status = status + ' ' + headers;
        });
    };
    $scope.changeRoute = function(url, forceReload) {
        $scope = $scope || angular.element(document).scope();
        if (forceReload || $scope.$$phase) {
            window.location = url;
        } else {
            $location.path(url);
            $scope.$apply();
        }
    };
    $scope.back = function() {
        window.history.back();
    };
});

consoleApp.controller("WhitelistController", function($scope, $http, $attrs) {

    $http.get('/rtb-console/ssp/whitelist/' + $attrs.partnerid).success(
        function(data) {
            $scope.partnerLists = data;
        });

    $scope.$watch('partnerLists', function(lists) {
        $scope.partnerListsAsJson = angular.toJson(lists);
    }, true);

    $scope.changeRoute = function(url, forceReload) {
        $scope = $scope || angular.element(document).scope();
        if (forceReload || $scope.$$phase) {
            window.location = url;
        } else {
            $location.path(url);
            $scope.$apply();
        }
    };

    $scope.back = function() {
    	window.history.back();
    };

    $scope.save = function() {
        console.log('saving ...' + $scope.partnerListsAsJson);
        var request = $http({
            url : '/rtb-console/ssp/whitelist',
            method : "POST",
            data : $scope.partnerListsAsJson,
            transformRequest : false,
            headers : {
                'Content-Type' : 'application/json'
            }
        }).success(function(data, status, headers, config) {
            $scope.movieResponse = data;
            if (data.validation == true) {
                $scope.changeRoute(data.redirectPath);
            } else {
                $scope.status = data;
            }
        }).error(function(data, status, headers, config) {
            $scope.status = status + ' ' + headers;
        });
    };
});

consoleApp.controller("SspPartnerController", function($scope, $http, $attrs) {

    $http.get('/rtb-console/ssp/partner/' + $attrs.partnerid).success(
        function(data) {
            $scope.partner = data;
        });
});

consoleApp.controller("DspPartnerController", function($scope, $http, $filter) {
    var orderBy = $filter('orderBy');
    $http.get('/rtb-console/dsp/list').success(
        function(data) {
            $scope.partners = data;
        });
    $scope.order = function(predicate) {
        $scope.predicate = predicate;
        $scope.reverse = ($scope.predicate === predicate) ? !$scope.reverse : false;
        $scope.partners = orderBy($scope.partners, predicate, $scope.reverse);
    };
    $scope.order('partnerName', true);
});

consoleApp.controller("DspPartnerNameController", function($scope, $http, $attrs) {

    $http.get('/rtb-console/dsp/name/' + $attrs.partnerid).success(
        function(data) {
            $scope.partner = data;
        });
});

consoleApp.controller("DspPartnerBasicConfigController", function($scope, $http, $attrs) {

    $http.get('/rtb-console/dsp/basicConfig/' + $attrs.partnerid).success(
        function(data) {
            $scope.basicConfig = data;
        });
});

consoleApp.controller("DspPartnerRequestConfigController", function($scope, $http, $attrs) {

    $http.get('/rtb-console/dsp/requestConfig/' + $attrs.partnerid).success(
        function(data) {
            $scope.requestConfig = data;
        });
});

consoleApp.controller("DspCreativeApprovalConfigController", function($scope, $http, $attrs) {

    $http.get('/rtb-console/dsp/creativeApprovalConfig/' + $attrs.partnerid).success(
        function(data) {
            $scope.creativeApprovalConfig = data;
        });
});

consoleApp.controller("DspBidOptimisationConfigController", function($scope, $http, $attrs) {

    $http.get('/rtb-console/dsp/bidOptimisationConfig/' + $attrs.partnerid).success(
        function(data) {
            $scope.bidOptimisationConfig = data;
        });
});

consoleApp.controller("DspDealOptimisationConfigController", function($scope, $http, $attrs) {

    $http.get('/rtb-console/dsp/dealOptimisationConfig/' + $attrs.partnerid).success(
        function(data) {
            $scope.dealOptimisationConfig = data;
        });
});

consoleApp.controller("DspTcfSettingsController", function($scope, $http, $attrs) {

    $http.get('/rtb-console/dsp/tcfSettings/' + $attrs.partnerid).success(
        function(data) {
            $scope.tcfSettings = data;
        });
});

consoleApp.controller("DspPartnerResponseConfigController", function($scope, $http, $attrs) {

    $http.get('/rtb-console/dsp/responseConfig/' + $attrs.partnerid).success(
        function(data) {
            $scope.responseConfig = data;
        });
});

consoleApp.controller("DspCapabilitiesController", function($scope, $http, $attrs) {

    $http.get('/rtb-console/dsp/capabilities/' + $attrs.partnerid).success(
        function(data) {
            $scope.capabilities = data;
        });
});

consoleApp.controller("DspTargetingController", function($scope, $http, $attrs) {

    $http.get('/rtb-console/dsp/targeting/' + $attrs.partnerid).success(
        function(data) {
            $scope.wrapper = data;
        });
});

consoleApp.controller("DspExternalIdConfigController", function($scope, $http, $attrs) {

    $http.get('/rtb-console/dsp/externalIdConfig/' + $attrs.partnerid).success(
        function(data) {
            $scope.externalIdConfig = data;
        });
});

consoleApp.controller("DspPartnerNotificationController", function($scope, $http, $attrs) {

    $http.get('/rtb-console/dsp/notification/' + $attrs.partnerid).success(
        function(data) {
            $scope.notification = data;
        });
});

consoleApp.controller("DspPartnerUserMatchingController", function($scope, $http, $attrs) {

    $http.get('/rtb-console/dsp/userMatching/' + $attrs.partnerid).success(
        function(data) {
            $scope.userMatching = data;
        });
});

consoleApp.controller("UserMatchPrioritiesController", function($scope, $http, $attrs) {

    $http.get('/rtb-console/dsp/umPriorities').success(
        function (data) {
            $scope.priorityLists = data;
        });
});

consoleApp.controller("DisplayUserMatchPrioritiesController", function($scope, $http, $attrs) {

    $http.get('/rtb-console/dsp/umDisplayPriorities').success(
        function (data) {
            $scope.partnerLists = data;
        });

    $scope.$watch('partnerLists', function(lists) {
        $scope.partnerListsAsJson = angular.toJson(lists);
    }, true);

    $scope.changeRoute = function(url, forceReload) {
        $scope = $scope || angular.element(document).scope();
        if (forceReload || $scope.$$phase) {
            window.location = url;
        } else {
            $location.path(url);
            $scope.$apply();
        }
    };

    $scope.back = function() {
        window.history.back();
    };

    $scope.save = function() {
        console.log('saving ...' + $scope.partnerListsAsJson);
        var request = $http({
            url : '/rtb-console/dsp/umDisplayPriorities',
            method : "POST",
            data : $scope.partnerListsAsJson,
            transformRequest : false,
            headers : {
                'Content-Type' : 'application/json'
            }
        }).success(function(data, status, headers, config) {
            $scope.movieResponse = data;
            if (data.validation == true) {
                $scope.changeRoute(data.redirectPath);
            } else {
                $scope.status = data;
            }
        }).error(function(data, status, headers, config) {
            $scope.status = status + ' ' + headers;
        });
    };
});


consoleApp.controller("VideoUserMatchPrioritiesController", function($scope, $http, $attrs) {

    $http.get('/rtb-console/dsp/umVideoPriorities').success(
        function (data) {
            $scope.partnerLists = data;
        });

    $scope.$watch('partnerLists', function(lists) {
        $scope.partnerListsAsJson = angular.toJson(lists);
    }, true);

    $scope.changeRoute = function(url, forceReload) {
        $scope = $scope || angular.element(document).scope();
        if (forceReload || $scope.$$phase) {
            window.location = url;
        } else {
            $location.path(url);
            $scope.$apply();
        }
    };

    $scope.back = function() {
        window.history.back();
    };

    $scope.save = function() {
        console.log('saving ...' + $scope.partnerListsAsJson);
        var request = $http({
            url : '/rtb-console/dsp/umVideoPriorities',
            method : "POST",
            data : $scope.partnerListsAsJson,
            transformRequest : false,
            headers : {
                'Content-Type' : 'application/json'
            }
        }).success(function(data, status, headers, config) {
            $scope.movieResponse = data;
            if (data.validation == true) {
                $scope.changeRoute(data.redirectPath);
            } else {
                $scope.status = data;
            }
        }).error(function(data, status, headers, config) {
            $scope.status = status + ' ' + headers;
        });
    };
});

consoleApp.controller("DspPartnerTechnicalController", function($scope, $http, $attrs) {

    $http.get('/rtb-console/dsp/technical/' + $attrs.partnerid).success(
        function(data) {
            $scope.technical = data;
        });
});

consoleApp.controller("OpenRtbValidatorController", function($scope, $http) {

    $scope.openRtb = {
        'requestType' : 'Bid Request',
        'version' : '2.5',
        'validateStroeer' : true,
        'validateCampaignKey': true,
        'usesAgnAvn': true
    };

    $scope.save = function() {
        var request = $http({
            url : '/rtb-console/tools/validate/openRtb',
            method : "POST",
            data : JSON.stringify($scope.openRtb),
            transformRequest : false,
            headers : {
                'Content-Type' : 'application/json'
            }
        }).success(function(data, status, headers, config) {
            $scope.status = data;
        }).error(function(data, status, headers, config) {
            $scope.status = status + ' ' + headers;
        });
        var p = $scope.openRtb.payload;
        var pJson = JSON.stringify(JSON.parse(p), undefined, 2);
        $scope.openRtb.payload = pJson;
    };
});

consoleApp.controller("VastValidatorController", function($scope, $http) {

    $scope.vast = {
        'schemaVersion' : '2'
    };

    $scope.save = function() {
        var request = $http({
            url : '/rtb-console/tools/validate/vast',
            method : "POST",
            data : JSON.stringify($scope.vast),
            transformRequest : false,
            headers : {
                'Content-Type' : 'application/json'
            }
        }).success(function(data, status, headers, config) {
            $scope.status = data;
            if (data.vast != "") {
                $scope.vast.payload = data.vast;
            }
        }).error(function(data, status, headers, config) {
            $scope.status = status + ' ' + headers;
        });
    };
});

consoleApp.controller("DspAlertsController", function($scope, $http, $attrs) {

    $http.get('/rtb-console/alerts/dsp/alerts/' + $attrs.partnerid).success(
        function(data) {
            $scope.response = data;
        });
});

var nativeRequestStructureMapping = {
    REQUEST_NATIVE_ASSETS: 'imp.native.request.native.assets',
    REQUEST_ASSETS: 'imp.native.request.assets',
    ASSETS: 'imp.native.assets'
};

consoleApp.filter('nativeRequestStructureDisplay', function () {
    return function (input) {
        return nativeRequestStructureMapping[ input ];
    }
});
