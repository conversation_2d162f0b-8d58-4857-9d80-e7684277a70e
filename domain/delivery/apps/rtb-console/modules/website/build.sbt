name := "RTB-Console-Web"
organization := "org.adscale"
import java.net.URL
import java.util.Properties
import scala.xml.Elem

val appProperties = settingKey[Properties]("The application properties")

appProperties := {
    val prop = new Properties()
    IO.load(prop, new File("conf/application.conf"))
    prop
}

version := appProperties.value.getProperty("app.version")

scalaVersion := "2.11.7"

routesGenerator := InjectedRoutesGenerator

val centralMirrorId = "labs-maven"
val mavenSettingsFile = Path.userHome / ".m2" / "settings.xml"
val settingsXml = mavenSettingsFile.asFile match {
    case mvn if mvn.canRead => Some(xml.XML.loadFile(mavenSettingsFile))
    case _ => None
}

resolvers := {
    extractMirrorUrl(settingsXml) match {
        case Some(mirrorUrl) => {
            println(s"Found mirror url $mirrorUrl for id $centralMirrorId (website Module)")
            Seq("central" at mirrorUrl)
        }
        case None => {
            println(s"No mirror found for id $centralMirrorId in settings.xml (website Module)")
            Seq.empty
        }
    }
}

javacOptions ++= Seq("-source", "1.8", "-target", "1.8")

libraryDependencies ++= {
    val akkaVersion = "2.4.11"
    Seq(
        ws,
        jdbc,
        "com.typesafe.akka" %% "akka-http-experimental"  % akkaVersion,
        "com.typesafe.play" %% "anorm" % "2.4.0" withSources,
        "org.postgresql" % "postgresql" % "42.2.8",
        "jp.t2v" %% "play2-auth" % "0.14.2",
        "jp.t2v" %% "play2-auth-test" % "0.14.2" % "test",
        "org.mindrot" % "jbcrypt" % "0.3m",
        "net.liftweb" %% "lift-json" % "2.6",
        "com.github.pathikrit" %% "better-files" % "2.16.0",
        "net.logstash.logback" % "logstash-logback-encoder" % "4.6",
        "com.github.danielwegener" % "logback-kafka-appender" % "0.1.0",
        "junit" % "junit" % "4.12" % "test",
        "org.scalatest" % "scalatest_2.11" % "2.2.4" % "test",
        "org.scalamock" %% "scalamock-scalatest-support" % "3.6.0" % "test",
//        "org.scalamock" %% "scalamock" % "4.1.0" % "test",
        "org.scalatestplus.play" %% "scalatestplus-play" % "1.5.1" % "test",
        "org.seleniumhq.selenium" % "selenium-java" % "2.47.1" % "test",
        "com.typesafe.akka" %% "akka-testkit" % akkaVersion % "test",
        "org.openrtb" % "openrtb-validator" % "2.3.1" withSources,
        "com.softwaremill.sttp.client3" %% "core" % "3.3.16",
        "com.softwaremill.sttp.client3" %% "json4s" % "3.3.16",
        "org.json4s" %% "json4s-native" % "4.0.0",
        "org.json4s" %% "json4s-jackson" % "4.0.0",
        "org.adscale" % "auth-token" % "1.0.0",
        "com.auth0" % "java-jwt" % "3.10.3",
        play.sbt.Play.autoImport.cache
    )
}

publishTo := Some("ssp-release-maven" at sys.env.getOrElse("ARTIFACTORY_HOST",
    "http://art.private.build.s9s.tech/artifactory/ssp-release-maven"))

sources in (Compile, doc) := Seq.empty

publishArtifact in (Compile, packageDoc) := false

def extractMirrorUrl(settingsXml: Option[Elem]): Option[String] = {
    settingsXml match {
        case Some(xml) => (xml \ "mirrors" \ "mirror")
                .find(mirror => (mirror \ "id").text == centralMirrorId)
                .map(mirror => (mirror \ "url").text)
        case None => None
    }
}
