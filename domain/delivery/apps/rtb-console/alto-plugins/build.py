import json
import os
import subprocess
from os.path import join, dirname

from alto.plugins import Plugin

DIR = dirname(__file__)
project_dir = dirname(DIR)
version_json = join(DIR, '..', 'version.json')
application_conf = join(DIR, '..', 'conf', 'application.conf')


class Other(Plugin):
    def snapshot(self, apps, version) -> None:
        self._update_version_json(version)
        self._write_app_conf_version(version)

    def unsnapshot(self, apps, version) -> None:
        self._update_version_json(version)
        self._write_app_conf_version(version)

    def build(self, apps, tags=None, quiet=False) -> bool:
        java_11_home = os.getenv('JAVA_11_HOME', None)
        if java_11_home is None:
            raise RuntimeException('JAVA 11 is required')

        os.chdir(project_dir)
        retcode = subprocess.call(['sbt', f'-java-home {java_11_home}', 'runMain util.DistributionCreator'])
        return retcode == 0

    def release(self, apps, version) -> None:
        self.unsnapshot(apps, version)
        self.build(apps)

    def unit_test(self, apps, incremental, quiet) -> bool:
        java_11_home = os.getenv('JAVA_11_HOME', None)
        if java_11_home is None:
            raise RuntimeException('JAVA 11 is required')

        os.chdir(project_dir)
        compile_success = subprocess.call(['sbt', f'-java-home {java_11_home}', 'clean', 'test:compile']) == 0
        if not compile_success:
            return False
        else:
            subprocess.call(f'sbt -java-home {java_11_home} test'.split())
            return True

    def _write_app_conf_version(self, version):
        # Update application.conf
        with open(application_conf) as a:
            app_conf_lines = a.readlines()
        with open(application_conf, 'w') as aw:
            for line in app_conf_lines:
                if 'app.version' in line:
                    aw.write('app.version=%s\n' % version)
                else:
                    aw.write(line)

    def _update_version_json(self, version):
        # Update version.json
        with open(version_json) as j:
            x = json.load(j)
        x['application']['version'] = version
        with open(version_json, 'w') as jw:
            json.dump(x, jw, indent=2, separators=(',', ': '))


def init(config):
    return Other(config, 'rtbconsole')
