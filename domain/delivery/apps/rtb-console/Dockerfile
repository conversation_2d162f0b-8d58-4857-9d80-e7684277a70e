FROM public.ecr.aws/amazonlinux/amazonlinux:2023 AS build

ARG ZIP_FILE=rtb-console.zip

ARG APP_DIR=/app
ARG APP_BIN_DIR=${APP_DIR}/bin \
    APP_CONF_DIR=${APP_DIR}/conf \
    APP_PROPS_DIR=${APP_DIR}/props

RUN dnf -y install unzip

COPY --from=600765955058.dkr.ecr.ap-southeast-2.amazonaws.com/ssp-scripts:REL-2411.04.5 /scripts/process_props.py ${APP_BIN_DIR}/

COPY docker/files/run.sh ${APP_BIN_DIR}/
COPY docker/files/logback.xml ${APP_CONF_DIR}/
COPY docker/props ${APP_PROPS_DIR}/

WORKDIR ${APP_DIR}

COPY docker/${ZIP_FILE} /tmp/
RUN unzip /tmp/${ZIP_FILE}

FROM public.ecr.aws/amazoncorretto/amazoncorretto:8-al2023

ARG STROEER_VERSION=UNKNOWN

ENV APP_DIR=/app \
    STROEER_VERSION=${STROEER_VERSION}
ENV APP_BIN_DIR=${APP_DIR}/bin \
    APP_CONF_DIR=${APP_DIR}/conf \
    APP_PROPS_DIR=${APP_DIR}/props
ENV PATH=${APP_BIN_DIR}:${PATH}

# Python for process_props.py
RUN set -x && \
    dnf -y install python && \
    dnf clean all

COPY --from=build --chown=nobody:nobody ${APP_DIR} ${APP_DIR}

WORKDIR ${APP_DIR}

USER nobody

ENTRYPOINT [ "run.sh" ]
