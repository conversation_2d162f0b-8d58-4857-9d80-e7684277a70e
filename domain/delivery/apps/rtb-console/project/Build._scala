import sbt._
import Keys._
import play. Project._
import com.typesafe.config._

object ApplicationBuild extends Build {
  
  lazy val root = (project in file(".")).enablePlugins(PlayScala)

  val conf = ConfigFactory.parseFile(new File("conf/application.conf")).resolve()
  
  scalaVersion = "2.11.1"

  val appName = conf.getString("app.name")
  val appVersion = conf.getString("app.version")

  val mainDependencies = Seq()

  val websiteDependencies = Seq(
    jdbc, anorm,
    // Add your project dependencies here,
    "postgresql" % "postgresql" % "9.1-901.jdbc4",
    "jp.t2v" %% "play2-auth" % "0.11.0",
    "org.mindrot" % "jbcrypt" % "0.3m",
    "net.liftweb" %% "lift-json" % "2.5.1",
    "junit" % "junit" % "4.11" % "test",
    "org.scalatest" % "scalatest_2.10" % "2.1.0" % "test",
    "org.scalatestplus" %% "play" % "1.1.0" % "test",
    "org.seleniumhq.selenium" % "selenium-java" % "2.35.0" % "test")

  lazy val website = play.Project(appName + "-Web", appVersion, websiteDependencies, path = file("modules/website"))

  lazy val main = play.Project(appName, appVersion, websiteDependencies).dependsOn(website).aggregate(website)
}