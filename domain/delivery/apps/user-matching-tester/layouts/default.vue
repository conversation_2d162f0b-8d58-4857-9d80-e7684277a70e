<template>
  <v-app dark>
    <v-toolbar
      fixed
      app
    >
      <v-toolbar-title v-text="title"/>
      <v-spacer></v-spacer>
      <v-tooltip bottom>
        <v-btn @click="onClick()" slot="activator"><v-icon class="mr-1">clear_all</v-icon>RESET ALL</v-btn>
        <span>Cookies and Users in Couchbase will not be cleared</span>
      </v-tooltip>
    </v-toolbar>
    <v-content>
      <v-container>
        <nuxt />
      </v-container>
    </v-content>
    <v-footer
      app
      class="px-3"
    >
      <span>&copy; 2018 </span>
      <v-spacer />
      <span>
        All you need to test user matching.
      </span>
    </v-footer>
  </v-app>
</template>

<script>
import { mapActions } from 'vuex';

export default {
  data() {
    return {
      title: 'User Matching Tester'
    };
  },
  methods: {
    async onClick() {
      await this.resetAll()
    },
    ...mapActions(['resetAll'])
  }
};
</script>
