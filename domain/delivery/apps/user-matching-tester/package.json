{"name": "user-matching-tester", "version": "1.0.0", "description": "Everything you need to test user matching", "author": "<PERSON><PERSON>", "private": true, "scripts": {"dev": "cross-env NODE_ENV=development nodemon server/index.js --watch server", "build": "nuxt build", "start": "cross-env NODE_ENV=production node server/index.js", "generate": "nuxt generate", "lint": "eslint --ext .js,.vue --ignore-path .gitignore .", "precommit": "npm run lint"}, "dependencies": {"@nuxtjs/axios": "^5.0.0", "cookie-parser": "^1.4.3", "cors": "^2.8.4", "cross-env": "^5.2.0", "debounce": "^1.2.0", "express": "^4.16.3", "nuxt": "^2.0.0", "pg": "^8.2.0", "query-string": "^6.2.0", "ramda": "^0.25.0", "socket.io": "^2.1.1", "socket.io-client": "^2.1.1", "urijs": "^1.19.2", "uuid": "^7.0.2", "vue-json-pretty": "^1.4.1", "vuetify": "^1.2.4", "vuex": "^3.0.1", "whatwg-fetch": "^3.0.0"}, "devDependencies": {"nodemon": "^2.0.4", "babel-eslint": "^8.2.1", "prettier": "1.14.3", "stylus": "^0.54.5", "stylus-loader": "^3.0.2"}}