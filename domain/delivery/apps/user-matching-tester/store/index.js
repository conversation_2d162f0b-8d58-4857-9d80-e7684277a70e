import Vuex from 'vuex'

const R = require('ramda');

require('whatwg-fetch');

const queryString = require('query-string');

const isSspInitMatchable = R.pipe(
  R.prop('cookie_sharing_url'),
  url => /http(s|):\/\/umt\.lsd\.test\/(.*)\/match/.test(url)
);

const createStore = () => {
  return new Vuex.Store({
    state: () => ({
      dsps: [],
      sspInitMatchRequests: [],
      dspInitMatchRequests: [],
    }),
    mutations: {
      setDsps(state, dsps) {
        state.dsps = dsps;
      },
      catchupRequests(state, { sspInitMatchRequests, dspInitMatchRequests }) {
        state.sspInitMatchRequests = sspInitMatchRequests;
        state.dspInitMatchRequests = dspInitMatchRequests;
      },
      newSspInitMatchRequest(state, sspInitMatchRequest) {
        state.sspInitMatchRequests = R.append(sspInitMatchRequest)(state.sspInitMatchRequests);
      },
      newDspInitMatchRequest(state, newDspInitMatchRequest) {
        state.dspInitMatchRequests = R.append(newDspInitMatchRequest)(state.dspInitMatchRequests);
      },
      clearSspInitMatchRequests(state) {
        state.sspInitMatchRequests = [];
      },
      clearDspInitMatchRequests(state) {
        state.dspInitMatchRequests = [];
      }
    },
    getters: {
      enabledDsps(state) {
        return R.pipe(
          R.filter(R.propEq('enabled', true)),
          R.map(dsp => R.merge(dsp, { isSspInitMatchable: isSspInitMatchable(dsp) })),
          R.sortWith([R.descend(R.prop('isSspInitMatchable'))])
        )(state.dsps);
      },
    },
    actions: {
      async loadDsps({ commit }) {
        commit('setDsps', await fetch(`/api/dsps`).then(r => r.json()));
        commit('catchupRequests', await fetch(`/api/requests`).then(r => r.json()));
      },
      async changeRespondMethod({ commit, dispatch }, options) {
        await fetch(`api/dsps/changeRespondMethod?${queryString.stringify(options)}`, {
          method: 'POST'
        })
          .then(r => r.json())
          .then(() => {
            dispatch('loadDsps');
          });
      },
      async resetAll({ dispatch }) {
        fetch('/api/resetAll', {
          method: 'POST'
        }).then(() => {
          console.log(`Everything should be cleared on server side`);
          dispatch(`loadDsps`);
        });
      }
    }
  });
};

export default createStore;
