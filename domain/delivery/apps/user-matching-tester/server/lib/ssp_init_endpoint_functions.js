const { newSspInitMatchRequest } = require('../datastore/requests_store');

const queryString = require('query-string');
const uuidv1 = require('uuid/v1');
const URI = require('urijs');

const R = require('ramda');

const SECONDS_TO_SLEEP = 5;

const { sleep } = require('./sleep_functions');

const genTpuid = dsp => [dsp.id, dsp.partner_name, uuidv1()].join('-');

const pickProps = R.pickAll(['originalUrl', 'cookies', 'query', 'params']);

const sspInitMatchRequestBase = ({ request, partner }) => ({
  partnerId: partner.id,
  request: pickProps(request)
});

const makeRedirectUrl = ({request, partner, tpuid}) => {
  const params = {
    tpuid: tpuid || genTpuid(partner),
    tpid: request.query.tpid
  };
  var url = new URI(request.query.cburl);
  url.addQuery(params);
  return url.toString();
};

const respondWithGenerateUser = ({ request, response, partner }) => {
  const tpuid = genTpuid(partner);
  const redirectUrl = makeRedirectUrl({request, partner, tpuid})

  const sspInitMatchRequest = Object.assign({}, sspInitMatchRequestBase({ request, partner }), {
    response: {
      tpuid,
      redirectUrl
    }
  });

  newSspInitMatchRequest(sspInitMatchRequest);

  console.log(`Redirecting to ${redirectUrl}`);

  response.redirect(redirectUrl);
};

const respondWithImage = ({ request, response, partner }) => {
  const sspInitMatchRequest = Object.assign({}, sspInitMatchRequestBase({ request, partner }), {
    response: {
      type: "image"
    }
  });

  newSspInitMatchRequest(sspInitMatchRequest);


  response.set('Content-Type', 'image/gif').end(Buffer.from([71, 73, 70, 56, 57, 97, 1, 0, 1, 0, 145, 255, 0, 255, 255, 255, 0, 0, 0, 192, 192,
    192, 0, 0, 0, 33, 249, 4, 1, 0, 0, 2, 0, 44, 0, 0, 0, 0, 1, 0, 1, 0, 0, 2, 2, 84, 1, 0, 59]));
};

const respondWithError = ({ request, response, partner }) => {
  const sspInitMatchRequest = {
    partnerId: partner.id,
    request: pickProps(request),
    response: {
      status: 500
    }
  };

  newSspInitMatchRequest(sspInitMatchRequest);

  response.status(500).end();
};

const respondWithGeneratedUserSlowly = options => {
  sleep(SECONDS_TO_SLEEP);
  respondWithGenerateUser(options);
};

const requestHandlerForRespondMethod = respondMethod => {
  switch (respondMethod) {
    case 'user':
      return respondWithGenerateUser;
    case 'image':
      return respondWithImage;
    case 'error':
      return respondWithError;
    case 'slow':
      return respondWithGeneratedUserSlowly;
    default:
      return undefined;
  }
};

module.exports = {
  requestHandlerForRespondMethod
};
