const { Pool } = require('pg');
const R = require('ramda');

const { getDspConfigById } = require("./dsp_config_store");

const pool = new Pool({
  user: 'postgres',
  host: process.env.POSTGRES_HOST || 'localhost',
  database: 'adscale_test'
});

const populateConfig = dsp => {
  const config = getDspConfigById(dsp.id);
  return Object.assign({}, dsp, { config })
};

const loadAllDsps = () =>
  pool
    .query('select * from v_rtb_dsp v, rtb_user_matching user_matching where v.id = user_matching.id')
    .then(r => r.rows)
    .then(R.map(populateConfig))
    .catch(e => {
      console.log(e.stack);
    });

const loadDspById = id => {
  return pool
    .query(`select * from v_rtb_dsp v, rtb_user_matching user_matching where v.id = user_matching.id and v.id = ${id}`)
    .then(
      R.pipe(
        R.prop('rows'),
        R.map(populateConfig),
        R.head
      )
    )
    .catch(e => {
      console.log(e.stack);
    });
};

module.exports = {
  loadAllDsps,
  loadDspById
};
