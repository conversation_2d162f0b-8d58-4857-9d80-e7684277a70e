const R = require("ramda");

let dspConfigs = {};

const upsertRespondMethod = (partnerId, respondMethod) => {
  const currentConfig = dspConfigs.partnerId || {};
  const newConfig = R.merge(currentConfig, { respondMethod })

  dspConfigs = R.merge(dspConfigs, {
    [partnerId]: newConfig
  })
};

const clearDspConfigs = () => dspConfigs = {};

const defaultDspConfig = {
  respondMethod: 'user'
}

const getDspConfigById = partnerId => dspConfigs[partnerId] || defaultDspConfig

const getAllDspConfigs = () => dspConfigs

module.exports = {
  upsertRespondMethod,
  getDspConfigById,
  getAllDspConfigs,
  clearDspConfigs
};
