const EventEmitter = require('events');

class RequestEventEmitter extends EventEmitter {}

const requestEventEmitter = new RequestEventEmitter();

const R = require('ramda');

let sspInitMatchRequests = [];
let dspInitMatchRequests = [];

const newSspInitMatchRequest = newSspInitMatchRequest => {
  sspInitMatchRequests = R.append(newSspInitMatchRequest, sspInitMatchRequests);

  requestEventEmitter.emit('newSspInitMatchRequest', newSspInitMatchRequest);
};

const newDspInitMatchRequest = dspInitMatchRequest => {
  dspInitMatchRequests = R.append(dspInitMatchRequest)(dspInitMatchRequests);

  requestEventEmitter.emit('newDspInitMatchRequest', dspInitMatchRequest);
};

const clearSspInitMatchRequests = () => {
  sspInitMatchRequests = [];
  requestEventEmitter.emit('clearSspInitMatchRequests');
};

const clearDspInitMatchRequests = () => {
  dspInitMatchRequests = [];
  requestEventEmitter.emit('clearDspInitMatchRequests');
};

const findAllRequests = () => ({
  sspInitMatchRequests,
  dspInitMatchRequests
});

module.exports = {
  newSspInitMatchRequest,
  newDspInitMatchRequest,
  clearSspInitMatchRequests,
  clearDspInitMatchRequests,
  requestEventEmitter,
  findAllRequests
}
