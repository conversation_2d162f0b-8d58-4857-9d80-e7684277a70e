const R = require('ramda');
const { loadDspById, loadAllDsps } = require('./datastore/postgres_store');
const { newDspInitMatchRequest } = require('./datastore/requests_store');

const { requestHandlerForRespondMethod } = require('./lib/ssp_init_endpoint_functions');

const sspInitMatchHandler = async (request, response) => {
  console.log(`Handling match request for partner (${request.params.partnerName})`);

  const partnerId = request.query.tpid;

  const partner = await loadDspById(partnerId);

  if (!partner) {
    const allPartnerIds = R.map(R.prop('id'))(await loadAllDsps());

    const message = `Partner (${partnerId}) not found. Available values are [${allPartnerIds}]`;
    console.log(message);

    response.status(404).json({ message: message });
    return;
  }

  const respondMethod = partner.config.respondMethod || 'user';

  const requestHandler = requestHandlerForRespondMethod(respondMethod);

  if (requestHandler) {
    requestHandler({ request, response, partner });
    return;
  }

  response.status(400).json({
    message: `Wrong respond method setup partner (${partnerId}), ${JSON.stringify(partner)}`
  });
};


const dspInitMatchCbHandler = async (request, response) => {
  const requestProps = R.pickAll(['originalUrl', 'cookies', 'query', 'params'])(request);

  console.log('Request to /dsp-init-cb');
  console.log(JSON.stringify(requestProps, null, 2));

  newDspInitMatchRequest(requestProps);

  response.status(200).end();
};

module.exports = {
  sspInitMatchHandler,
  dspInitMatchCbHandler
};
