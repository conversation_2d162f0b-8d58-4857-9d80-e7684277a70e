const express = require('express');
const apiRoutes = express.Router();
const { loadAllDsps } = require('../datastore/postgres_store');

const {
  findAllRequests,
  clearSspInitMatchRequests,
  clearDspInitMatchRequests
} = require('../datastore/requests_store');

const { getAllDspConfigs, clearDspConfigs } = require('../datastore/dsp_config_store');

const { upsertRespondMethod } = require('../datastore/dsp_config_store');

const { sspInitMatchHandler, dspInitMatchCbHandler } = require('../endpoint_handlers');

apiRoutes.get('/:partnerName/match', sspInitMatchHandler);

apiRoutes.get('/dsps', async (request, response) => {
  response.json(await loadAllDsps());
});

apiRoutes.get('/dspConfigs', (request, response) => {
  response.json(getAllDspConfigs());
});

apiRoutes.post('/dsps/changeRespondMethod', async (request, response) => {
  const { partnerId, respondMethod } = request.query;
  upsertRespondMethod(partnerId, respondMethod);

  response.json(request.query);
});

apiRoutes.get('/requests', async (request, response) => {
  response.json(findAllRequests());
});

apiRoutes.post('/clearSspInitMatchRequests', async (request, response) => {
  console.log('Clearing all clearSspInitMatchRequests.');
  clearSspInitMatchRequests();
  response.status(200).end();
});

apiRoutes.post('/clearDspInitMatchRequests', async (request, response) => {
  console.log('Clearing all clearDspInitMatchRequests.');
  clearDspInitMatchRequests();
  response.status(200).end();
});

apiRoutes.post('/resetAll', async (request, response) => {
  console.log('Resetting everything on server side');

  clearDspInitMatchRequests();
  clearSspInitMatchRequests();
  clearDspConfigs();

  response.status(200).end();
});

apiRoutes.get('/dsp-init-cb/:partnerId', dspInitMatchCbHandler);

exports.apiRoutes = apiRoutes;
