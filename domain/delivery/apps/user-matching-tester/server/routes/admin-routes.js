const express = require('express')
const bodyParser = require('body-parser')

let status = ['OK', 'MAINT', 'INITIALISING']
let currentIndex = 0

exports.adminRoutes = () => {
  const adminRoutes = express.Router()
  adminRoutes.use(bodyParser.text())

  adminRoutes.get('/up', function(req, res) {
    var statusCode = currentIndex === 'OK' ? 200 : 400
    res.writeHeader(statusCode, { 'Content-Type': 'text/plain' })
    res.end()
  })

  adminRoutes.get('/status', function(req, res) {
    res.writeHeader(200, { 'Content-Type': 'text/plain' })
    res.write(status[currentIndex])
    res.end()
  })

  adminRoutes.post('/status', function(req, res) {
    try {
      let newStatus = req.body.toString().trim()
      let newIndex = status.indexOf(newStatus)

      if (newIndex >= 0) {
        console.log('setting application status to: ' + status[newIndex])
        currentIndex = newIndex
        res.writeHeader(200, { 'Content-Type': 'text/plain' })
        res.end()
      } else {
        console.log('invalid status: ' + newStatus)
        res.writeHeader(400, { 'Content-Type': 'text/plain' })
        res.end()
      }
    } catch (e) {
      console.log('error: ' + e)
      res.writeHeader(400, { 'Content-Type': 'text/plain' })
      res.end()
    }
  })

  adminRoutes.get('/health', function(req, res) {
    let overalHealth = { healthy: true }
    res.writeHeader(200, { 'Content-Type': 'application/json' })
    res.write(JSON.stringify(overalHealth))
    res.end()
  })

  return adminRoutes
}
