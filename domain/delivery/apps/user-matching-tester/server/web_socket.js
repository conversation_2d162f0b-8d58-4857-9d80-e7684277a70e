const io = require('socket.io');

const { requestEventEmitter } = require('../server/datastore/requests_store');

exports.createNewWss = server => {
  const wss = io(server);
  wss.on('connection', socket => {
    requestEventEmitter.on('newSspInitMatchRequest', newRequests => {
      socket.emit('newSspInitMatchRequest', newRequests);
    });

    requestEventEmitter.on('newDspInitMatchRequest', dspInitMatchRequest => {
      socket.emit('newDspInitMatchRequest', dspInitMatchRequest);
    });

    requestEventEmitter.on('clearDspInitMatchRequests', () => {
      socket.emit('clearDspInitMatchRequests');
    });

    requestEventEmitter.on('clearSspInitMatchRequests', () => {
      socket.emit('clearSspInitMatchRequests');
    });
  });

  return wss;
};
