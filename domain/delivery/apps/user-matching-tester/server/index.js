const express = require('express');
const http = require('http');
var https = require('https');
const cors = require('cors');
const fs = require('fs');
const cookie = require('cookie-parser');
const consola = require('consola');
const { Nuxt, Builder } = require('nuxt');
const app = express();

const hostname = process.env.HOST || '127.0.0.1';
const port = process.env.PORT || 6186;
const httpsPort = process.env.HTTPS_PORT || 6187;
const devCertDir = process.env.LABS_DEV_ROOT + '/ssp-core/etc/certs/lsd.test';
const certFile = process.env.HTTPS_CERT_FILENAME || devCertDir + '/star_lsd_test.crt';
const keyFile = process.env.HTTPS_KEY_FILENAME || devCertDir + '/star_lsd_test.key';

const { apiRoutes } = require('./routes/api-routes');
const { adminRoutes } = require('./routes/admin-routes');

const { createNewWss } = require('./web_socket');

var privateKey = fs.readFileSync(keyFile).toString();
var certificate = fs.readFileSync(certFile).toString();
var creds = {key: privateKey, cert: certificate};

const httpServer = http.Server(app);
const httpsServer = https.Server(creds, app);

createNewWss(httpServer);
createNewWss(httpsServer);

app.use(cors());
app.use(cookie());

// Import and Set Nuxt.js options
let config = require('../nuxt.config.js');
config.dev = !(process.env.NODE_ENV === 'production');

async function start() {
  // Init Nuxt.js
  const nuxt = new Nuxt(config);

  // Build only in dev mode
  if (config.dev) {
    const builder = new Builder(nuxt);
    await builder.build();
  }

  app.use('/api', apiRoutes);
  app.use('/admin', adminRoutes());

  // Give nuxt middleware to express
  app.use(nuxt.render);

  httpServer.listen(port, hostname);
  httpsServer.listen(httpsPort, hostname);
  consola.ready({
    message: `Server listening on http://${hostname}:${port} and https://${hostname}:${httpsPort}`,
    badge: true
  });
}

start();
