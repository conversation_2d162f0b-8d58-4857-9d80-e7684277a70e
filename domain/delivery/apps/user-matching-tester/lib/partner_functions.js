const uuidv1 = require('uuid/v1');

const queryString = require('query-string');

export const genTpuid = dsp => [dsp.id, dsp.partner_name, uuidv1()].join('-');

export const createDspInitMatchingUrl = dsp => {
  const params = {
    uid: "__ADSCALE_USER_ID__",
    ga: "${GDPR}",
    gc: "${GDPR_CONSENT_123}"
  }
  return `http://umt.lsd.test/api/dsp-init-cb/${dsp.id}?${queryString.stringify(params)}`;
}
