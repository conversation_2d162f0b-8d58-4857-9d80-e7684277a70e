<template>
  <v-card class="elevation-5">
    <v-card-text>
      <v-layout row wrap align-center>
        <div class="title mr-1">Publisher Init Matching</div>
        <v-container >
          <v-switch v-model="gdprApplies" :label="`GDPR Applies: ${gdprApplies.toString()}`"></v-switch>
          <v-flex xs12 sm6 md3>
            <v-text-field
              label="TCF String"
              v-model="gdprConsent"
            ></v-text-field>
          </v-flex>
          <v-flex xs12 sm12 md12>
            <v-text-field
              label="Callback URL String"
              v-model="cburl"
            ></v-text-field>
          </v-flex>
        </v-container>
        <match-requester :url="stroeerUserMatchUrl" />
      </v-layout>
      <v-layout>
        <div class="subheading grey--text">
          There will be a request send to
          <span class="white--text">
            {{ stroeerUserMatchUrl }}
          </span>, the network console will have more information on this request as well as where it is redirected to and what values were included.
        </div>
      </v-layout>
    </v-card-text>
  </v-card>
</template>

<script>
import MatchRequester from './MatchRequester';

const queryString = require('query-string');

export default {
  data() {
    return {
      gdprApplies: false,
      gdprConsent: "",
      cburl: "https://httpbin.org/get?aid=__Account_ID__&uid=__STROEER_USER_ID__"
    }
  },
  components: {
    MatchRequester
  },
  computed: {
    stroeerUserMatchUrl() {
      const paramString = queryString.stringify({
        tpid: '101',
        gdpr: this.gdprApplies ? '1' : '0',
        gdpr_consent: this.gdprConsent,
        cburl: this.cburl
      });
      return `https://user-matcher.lsd.test/su?${paramString}`;
    }
  }
};
</script>

<style scoped>
</style>
