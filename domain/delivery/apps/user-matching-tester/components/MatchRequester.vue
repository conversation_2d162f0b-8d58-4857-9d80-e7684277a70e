<template>
  <div>
    <iframe :ref="iframeRef" hidden/>
    <v-btn @click="send" :disabled="!url">Send</v-btn>
  </div>
</template>

<script>
import uuid from 'uuid/v1';

export default {
  props: ['url'],
  computed: {
    iframeRef() {
      return uuid();
    }
  },
  methods: {
    send() {
      if (!this.url) return;
      // Need to change the url of iframe to force it reload
      const iframe = this.$refs[this.iframeRef];

      if (!iframe.src) {
        iframe.src = this.url;
      } else {
        iframe.src = this.url + '';
      }
    }
  }
};
</script>
