<template>
  <v-tabs
    v-model="active"
    dark
    centered
    slider-color="yellow"
  >
    <v-tab>
      <v-badge right large overlap color="cyan">
        <span slot="badge"> {{ sspInitMatchRequests.length }}</span>
        SSP Init Matching
      </v-badge>
    </v-tab>
    <v-tab>
      <v-badge right large overlap color="cyan">
        <span slot="badge"> {{ dspInitMatchRequests.length }}</span>
        DSP Init Matching
      </v-badge>

    </v-tab>
    <v-tab>
      IBB Init Matching
    </v-tab>
    <v-tab>
      Publisher init Matching
    </v-tab>
    <v-tab-item>
      <ssp-init-matching/>
    </v-tab-item>
    <v-tab-item>
      <dsp-init-matching/>
    </v-tab-item>
    <v-tab-item>
      <ibb-init-match/>
    </v-tab-item>
    <v-tab-item>
      <user-information/>
    </v-tab-item>
    <v-tab-item>
      <publisher-init-matching/>
    </v-tab-item>
  </v-tabs>

</template>

<script>
import SspInitMatching from '../components/ssp-init/SspInitMatching';
import DspInitMatching from '../components/dsp-init/DspInitMatching';
import IbbInitMatch from '../components/IbbInitMatch';
import PublisherInitMatching from '../components/PublisherInitMatching.vue'

import { mapState } from 'vuex';

export default {
  data() {
    return {
      active: null
    };
  },
  computed: {
    ...mapState(['sspInitMatchRequests', 'dspInitMatchRequests'])
  },
  components: {
    SspInitMatching,
    DspInitMatching,
    IbbInitMatch,
    PublisherInitMatching
  }
};
</script>

<style scoped>
</style>
