<template>
    <v-card>
      <v-card-title>
        <span class="title">Initialize match for certain DSP</span>
        <v-spacer></v-spacer>
        <v-btn @click="initializeClearDspInitMatchRequests()"><v-icon class="mr-1">clear_all</v-icon>Clear Requests</v-btn>
      </v-card-title>
      <v-container >
        <v-switch v-model="gdprApplies" :label="`GDPR Applies: ${gdprApplies.toString()}`"></v-switch>
        <v-flex xs12 sm6 md3>
          <v-text-field
            label="TCF String"
            v-model="gdprConsent"
          ></v-text-field>
        </v-flex>
      </v-container>
      <v-card-text>
        <v-layout row wrap>
          <dsp-init-partner-board v-for="dsp in enabledDsps" :dsp="dsp" :key="dsp.id" :gdpr="gdprApplies" :gdpr_consent="gdprConsent" />
        </v-layout>
      </v-card-text>
    </v-card>
</template>

<script>
import { mapGetters, mapMutations } from 'vuex';
import DspInitPartnerBoard from './DspInitPartnerBoard'
import { socket } from '../../plugins/socket.io';

export default {
  beforeMount() {
    socket.on('newDspInitMatchRequest', dspInitMatchRequest => {
      this.newDspInitMatchRequest(dspInitMatchRequest)
    });

    socket.on('clearDspInitMatchRequests', () => {
      this.clearDspInitMatchRequests();
    })
  },
  data() {
    return {
      gdprApplies: false,
      gdprConsent: "",
    }
  },
  components: {
    DspInitPartnerBoard
  },
  computed: {
    ...mapGetters(['enabledDsps'])
  },
  methods: {
    async initializeClearDspInitMatchRequests() {
      await fetch('/api/clearDspInitMatchRequests', {
        method: 'POST'
      }).then(() => {
        console.log('DspInitMatchRequests Cleared');
      });
    },
    ...mapMutations(["newDspInitMatchRequest", "clearDspInitMatchRequests"])
  }
};
</script>

<style scoped>
</style>
