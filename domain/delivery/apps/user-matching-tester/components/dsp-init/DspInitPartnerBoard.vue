<template>
  <v-flex xs12 sm12 md6 xl4 class="pa-2">
    <v-card class="elevation-5">
      <div class="pl-3 pt-3">
        <div class="title d-block">{{ dsp.partner_name}} ({{ dsp.id }})</div>
        <span class="grey--text dsp-init-url">
            {{ dspInitUrl }}
          </span>
      </div>
      <v-card-text>
        <match-requester :url="dspInitUrl" />
        <div v-if="mostRecentRequest">
          <div>Most recent callback Request:</div>
          <vue-json-pretty :data="mostRecentRequest" />
        </div>
      </v-card-text>
    </v-card>
  </v-flex>
</template>

<script>
import {
  createDspInitMatchingUrl,
  genTpuid
} from '../../lib/partner_functions';

import VueJsonPretty from 'vue-json-pretty';
import MatchRequester from '../MatchRequester';

import { dspInitMatchingEndpoint } from '../../lib/env_functions';
import queryString from 'query-string';

import { mapState } from 'vuex';

const R = require('ramda');

export default {
  props: ['dsp', 'gdpr', 'gdpr_consent'],
  data() {
    return {
      sent: false
    };
  },
  components: {
    VueJsonPretty,
    MatchRequester
  },
  computed: {
    dspInitUrl() {
      const tpuid = genTpuid(this.dsp);

      const params = {
        tpuid,
        tpid: this.dsp.id,
        cburl: createDspInitMatchingUrl(this.dsp),
        gdpr: this.gdpr ? '1' : '0',
        gdpr_consent: this.gdpr_consent
      };

      return `${dspInitMatchingEndpoint}?${queryString.stringify(params, {
        sort: false
      })}`;
    },
    mostRecentRequest() {
      const requestsForMe = R.filter(
        req => req.params.partnerId === this.dsp.id
      )(this.dspInitMatchRequests);

      return R.last(requestsForMe);
    },
    ...mapState(['dspInitMatchRequests'])
  }
};
</script>

<style scoped>
.dsp-init-url {
  overflow-wrap: break-word;
}
</style>
