<template>
  <v-card class="elevation-5">
    <v-card-text>
      <v-layout row wrap align-center>
        <div class="title mr-1">IBB Init Matching</div>
        <v-container >
          <v-switch v-model="gdprApplies" :label="`GDPR Applies: ${gdprApplies.toString()}`"></v-switch>
          <v-flex xs12 sm6 md3>
            <v-text-field
              label="TCF String"
              v-model="gdprConsent"
            ></v-text-field>
          </v-flex>
        </v-container>
        <match-requester :url="ibbMatchUrl" />
      </v-layout>
      <v-layout>
        <div class="subheading grey--text">
          There will be a request send to
          <span class="white--text">
            {{ ibbMatchUrl }}
          </span>, but nothing you can check here. Please check the Javascript console for more information.
        </div>
      </v-layout>
    </v-card-text>
  </v-card>
</template>

<script>
import MatchRequester from './MatchRequester';

const queryString = require('query-string');
const uuid = require('uuid/v1');

export default {
  data() {
    return {
      gdprApplies: false,
      gdprConsent: ""
    }
  },
  components: {
    MatchRequester
  },
  computed: {
    ibbMatchUrl() {
      const paramString = queryString.stringify({
        tpid: '101',
        tpuid: `ibb-${uuid()}`,
        gdpr: this.gdprApplies ? '1' : '0',
        gdpr_consent: this.gdprConsent
      });
      return `https://user-matcher.lsd.test/adscale-ih/dum?${paramString}`;
    }
  }
};
</script>

<style scoped>
</style>
