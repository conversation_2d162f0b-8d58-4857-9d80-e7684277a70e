<template>
  <v-card>
    <v-card-text>
      <vue-json-pretty :data="sspInitMatchRequestToShow" />
    </v-card-text>
  </v-card>
</template>

<script>
const R = require('ramda');
import VueJsonPretty from 'vue-json-pretty'

export default {
  props: ['sspInitMatchRequest'],
  components: {
    VueJsonPretty
  },
  computed: {
    sspInitMatchRequestToShow() {
      return R.omit(["partnerId"])(this.sspInitMatchRequest)
    }
  }
};
</script>

<style scoped>

</style>
