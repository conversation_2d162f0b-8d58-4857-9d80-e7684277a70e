<template>
  <v-flex xs12 sm12 md6 xl4 class="pa-2">
    <v-card :class="['elevation-5', {'unmatchable': !dsp.isSspInitMatchable}]">
      <div class="pl-3 pt-3">
        <div class="title d-block">{{ dsp.partner_name}} ({{ dsp.id }})
          <v-tooltip v-if="!dsp.isSspInitMatchable" top>
            <v-icon dark slot="activator">error</v-icon>
            <span>UMT works with lsd only. Matching Url must start with `http://umt.lsd.test/${partnerName}/match?${query}`</span>
          </v-tooltip>
        </div>
        <div class="grey--text subheading match-url">{{ dsp.cookie_sharing_url}}</div>
        <v-spacer></v-spacer>
        <v-select
          :value="dsp.config.respondMethod"
          :items="items"
          label="How should I respond to user matching request?"
          item-text="d"
          item-value="v"
          @change="onChangeRespondMethod"
         />
      </div>
      <ssp-partner-match-requests-panel :dsp="dsp" />
    </v-card>
  </v-flex>
</template>

<script>
import SspPartnerMatchRequestsPanel from './SspPartnerMatchRequestsPanel';

import { mapActions } from 'vuex';

export default {
  props: ['dsp'],
  data() {
    return {
      items: [
        { v: 'user', d: 'Match which generated user' },
        { v: 'image', d: '1x1 Image' },
        { v: 'slow', d: 'Slow Response' },
        { v: 'error', d: 'Error' }
      ]
    };
  },
  components: {
    SspPartnerMatchRequestsPanel
  },
  methods: {
    onChangeRespondMethod(respondMethod) {
      const options = {
        partnerId: this.dsp.id,
        respondMethod: respondMethod
      };

      this.changeRespondMethod(options);
    },
    ...mapActions(["changeRespondMethod"])
  }
};
</script>

<style scoped>
.unmatchable {
  border: 1px red solid;
}

.match-url {
  overflow-wrap: break-word;
}
</style>
