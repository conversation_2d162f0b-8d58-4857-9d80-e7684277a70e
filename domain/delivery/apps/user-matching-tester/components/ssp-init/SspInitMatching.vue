<template>
  <v-card class="elevation-5">
    <v-card-title>
      <span class="title">
        <span class="py-3"> All {{ enabledDsps.length }} enabled partners. </span>
        <span class="grey--text body-1">There are {{ dsps.length - enabledDsps.length }} filtered out.</span>
      </span>
      <v-spacer />
      <v-btn @click="initializeClearSspInitMatchRequests()"><v-icon class="mr-1">clear_all</v-icon>Clear Requests</v-btn>
    </v-card-title>
    <v-card-text>
      <v-layout row wrap>
        <ssp-init-partner-board v-for="dsp in enabledDsps" :key="`partner-${dsp.id}`" :dsp="dsp"/>
      </v-layout>
    </v-card-text>
  </v-card>
</template>

<script>
import SspInitPartnerBoard from './SspInitPartnerBoard';
import { socket } from '../../plugins/socket.io';

import { mapActions, mapState, mapMutations, mapGetters } from 'vuex';

export default {
  beforeMount() {
    this.loadDsps();
    socket.on('newSspInitMatchRequest', sspInitMatchRequest => {
      this.newSspInitMatchRequest(sspInitMatchRequest);
    });
    socket.on('clearSspInitMatchRequests', () => {
      this.clearSspInitMatchRequests();
    });
  },
  components: {
    SspInitPartnerBoard
  },
  computed: {
    ...mapGetters(['enabledDsps']),
    ...mapState(['dsps'])
  },
  methods: {
    async initializeClearSspInitMatchRequests() {
      await fetch('/api/clearSspInitMatchRequests', {
        method: 'POST'
      }).then(() => {
        console.log('SspInitMatchRequests Cleared');
      });
    },
    ...mapActions(['loadDsps']),
    ...mapMutations(['newSspInitMatchRequest', 'clearSspInitMatchRequests'])
  }
};
</script>
