<template>
  <v-card-text>
      <div class="mt-3">
        <span v-if="countOfRequest === 0">No requests sent to this partner yet.</span>
        <span v-else>
          <v-chip small>{{ countOfRequest }}</v-chip> requests received
        </span>
      </div>
      <div v-if="countOfRequest > 0">
        Most Recent Request:
        <ssp-init-req-resp :sspInitMatchRequest="lastSspInitMatchRequestForMe"/>
        <v-dialog
          v-model="dialog"
          v-if="countOfRequest > 1"
          @keydown.esc="dialog = false">
          <v-btn
            class="mt-3"
            slot="activator">
            SHOW ALL
          </v-btn>
          <v-card>
            <v-card-title>
              <div class="title">
                All {{ countOfRequest }} requests for {{ dsp.partner_name}} ({{ dsp.id }})
              </div>
            </v-card-title>
            <v-card-text>
              <ssp-init-req-resp v-for="sspInitMatchRequest in sspInitMatchRequestForMe.reverse()" :key="sspInitMatchRequest.response.partnerUserId"
                                  :sspInitMatchRequest="sspInitMatchRequest"/>
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn
                color="primary"
                @click="dialog = false"
              >
                CLOSE
              </v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
      </div>
    </v-card-text>
</template>

<script>
import SspInitReqResp from './SspInitReqResp';
import { mapState } from 'vuex';

const R = require('ramda');

export default {
  props: ['dsp'],
  data() {
    return {
      dialog: false
    }
  },
  computed: {
    sspInitMatchRequestForMe() {
      return R.filter(R.propEq('partnerId', this.dsp.id))(this.sspInitMatchRequests);
    },
    lastSspInitMatchRequestForMe() {
      return R.last(this.sspInitMatchRequestForMe);
    },
    countOfRequest() {
      return this.sspInitMatchRequestForMe.length;
    },
    ...mapState(['sspInitMatchRequests'])
  },
  components: {
    SspInitReqResp
  }
};
</script>

