FROM node:20-slim as builder

RUN apt-get update  \
    && apt-get install -y git python3 make gcc build-essential nodejs

# Create app directory
RUN mkdir -p /usr/src/app/logs
WORKDIR /usr/src/app

# Install app dependencies
COPY package.json /usr/src/app/
RUN npm install

COPY docker/files/entrypoint.sh /etc/bin/

# Bundle app source
COPY . /usr/src/app

#RUN chown -R adscale:adscale /usr/src/app

# Certs should be mounted in as a volume
ENV POSTGRES_HOST=postgres.service.ssp.consul \
    IH_HOST=ih.service.ssp.consul \
    IH_JMX_PORT=11083 \
    HTTPS_CERT_FILENAME=/certs/cert.crt \
    HTTPS_KEY_FILENAME=/certs/cert.key

ENTRYPOINT [ "/etc/bin/entrypoint.sh" ]

#USER adscale
