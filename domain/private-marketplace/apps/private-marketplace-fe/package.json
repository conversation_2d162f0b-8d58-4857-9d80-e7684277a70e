{"name": "aa", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"bulma": "0.9.4", "core-js": "^3.8.3", "json-diff-ts": "4.0.1", "md5": "^2.2.1", "vue": "^2.6.6", "vue-class-component": "^7.2.3", "vue-property-decorator": "^9.1.2", "vue-resource": "^1.5.1", "vue2-ace-editor": "0.0.13"}, "devDependencies": {"@babel/preset-env": "7.3.4", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-service": "~5.0.8", "@vue/eslint-config-typescript": "^9.1.0", "babel-eslint": "^10.0.1", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "typescript": "~4.5.5", "vue-template-compiler": "^2.5.21", "@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended", "@vue/typescript"], "rules": {}, "parserOptions": {"parser": "@typescript-eslint/parser"}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}