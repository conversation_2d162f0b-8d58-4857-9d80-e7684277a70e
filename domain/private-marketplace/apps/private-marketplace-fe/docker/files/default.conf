server {
    listen      5493;
    server_name  localhost;

    #charset koi8-r;
    #access_log  /var/log/nginx/host.access.log  main;

    # frontend static pages
    location / {
        root   /usr/share/nginx/html;
        index  index.html;
    }

    # defined to silence logs
    location /up {
        return 200 "OK";
    }

    # Deal Service endpoints redirect
    location /ds/ {
        gzip on;
        proxy_set_header Accept-Encoding "";
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_pass http://${DEAL_SERVICE_API}/;
    }

    location /deal-monitor/ {
        gzip on;
        proxy_set_header Accept-Encoding "";
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_pass http://${DEAL_MONITOR_API}/;
    }

    # redirect server error pages to the static page /50x.html
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }
}
