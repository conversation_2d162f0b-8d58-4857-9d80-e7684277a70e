

<template>
  <div>
    <div class="columns">
      <div class="column">
        <div class="control is-expanded">
          <div><b>Search by Private Auction/Deal Id</b></div>
          <div class="columns">
            <div class="column is-two-thirds">
              <div class="control is-expanded">
                <input
                    class="input is-fullwidth"
                    type="text"
                    v-model="privateAuctionId"
                    placeholder="Private Auction/Deal Id"
                />
              </div>
            </div>
            <div class="column" >
              <div>
                <a class="button is-primary" v-on:click="fetchEventLogsByPrivateAuctionId">Search</a>
              </div>
            </div>
          </div>

          <div><b>Search by Event Id</b></div>
          <div class="columns">
            <div class="column is-two-thirds">
              <div class="control is-expanded">
                <input
                    class="input is-fullwidth"
                    type="text"
                    v-model="eventId"
                    placeholder="Event Id"
                />
              </div>
            </div>
            <div class="column" >
              <div>
                <a class="button is-primary" v-on:click="fetchEventLogsByEventId">Search</a>
              </div>
            </div>
          </div>

          <div style="margin-top: 200px"><b>Get Slots that Private Auction/Deal targets on</b></div>
          <div class="columns">
            <div class="column is-two-thirds">
              <div class="control is-expanded">
                <input
                    class="input is-fullwidth"
                    type="text"
                    v-model="privateAuctionIdForSlots"
                    placeholder="Private Auction/Deal Id"
                />
              </div>
            </div>
            <div class="column" >
              <div>
                <a class="button is-primary" v-on:click="getSlotsDealTargetsOn">Search</a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="column" >
        <div>{{this.failureInfo}}</div>
        <div>
          <editor
              class="box"
              v-model="editorInfo"
              @init="editorInit"
              lang="json"
              theme="chrome"
              height="1046"
          ></editor>
        </div>
      </div>
    </div>
  </div>

</template>

<script>

import editor from "vue2-ace-editor";

export default {
  name: 'Hub',
  components: {editor},
  data() {
    return {
      privateAuctionId: '',
      eventId: '',
      privateAuctionIdForSlots: '',
      editorInfo: '',
      failureInfo: ''
    }
  },
  created() {

  },
  methods: {
    fetchEventLogsByPrivateAuctionId() {
      this.$http.get('deal-monitor/eventLogs/byPrivateAuctionId',
  {params: {
          privateAuctionId: this.privateAuctionId
      }}).then(response => {
            return response.json()
          },
          (error) => {
            console.log(error);
            this.editorInfo = ''
            this.failureInfo = "Something went wrong"
          }
      ).then(data => {
        console.log(data)
        this.editorInfo = JSON.stringify(data, null, 4);
        this.failureInfo = ''
      });
      console.log("here")

    },
    fetchEventLogsByEventId() {
      this.$http.get('deal-monitor/eventLogs/byEventId',
    {params: {
            eventId: this.eventId
        }})
          .then(response => {
            return response.json()
          },
  (error) => {
            console.log(error);
            this.editorInfo = ''
            this.failureInfo = "Something went wrong"
          }
          ).then(data => {
        this.editorInfo = JSON.stringify(data, null, 4);
        this.failureInfo = ''
      });
    },
    getSlotsDealTargetsOn() {
      this.$http.get('deal-monitor/slots/byPrivateAuctionId',
          {params: {
              privateAuctionId: this.privateAuctionIdForSlots
            }})
          .then(response => {
            return response.json()
          },
          (error) => {
            console.log(error);
            this.editorInfo = ''
            this.failureInfo = "Something went wrong"
          }
    ).then(data => {
        this.editorInfo = JSON.stringify(data, null, 4);
        this.failureInfo = ''
      });
    },
    editorInit() {
      require("brace/ext/language_tools"); // language extension prerequsite...
      require("brace/mode/json"); // language
      require("brace/theme/chrome");
    }
  }
}

</script>

<style scoped>

</style>