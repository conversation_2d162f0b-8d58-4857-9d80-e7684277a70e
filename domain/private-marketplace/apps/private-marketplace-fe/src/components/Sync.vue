<template>
  <div>
    <div class="box">
      <div class="tile is-ancestor">
        <div class="tile is-parent">
          <div class="tile is-child">
            <p class="title is-4">Tools</p>
            <nav class="panel">
              <p class="panel-heading">Sync</p>
              <div class="panel-block">
                <div class="control">
                  <a class="button is-primary" v-on:click="syncCouchBase"
                    >Sync all to Couchbase
                  </a>
                  <p class="panel-text">
                    {{ syncResponse }}
                  </p>
                </div>
              </div>
              <div class="panel-block">
                <div class="field has-addons is-expanded">
                  <div class="control is-expanded">
                    <input
                        class="input is-fullwidth"
                        type="text"
                        v-model="dealIdForCompare"
                        placeholder="Deal ID / Private Auction Id"
                    />
                  </div>
                  <div class="control">
                    <a class="button is-primary" v-on:click="compareSingleTargeting">
                      Compare Single Targeting
                    </a>
                  </div>
                  <div class="control">
                    {{ compareResponse }}
                  </div>
                </div>
              </div>
              <div class="panel-block">
                <div class="control">
                  <a class="button is-primary" v-on:click="compareAllTargetings"
                  >Compare All Targetings
                  </a>
                </div>
              </div>
            </nav>
            <div class="box">
              <div class="tile is-ancestor">
                <div class="tile is-parent">
                  <div class="tile is-child">
                    <p class="title is-4">Compare</p>
                    <div class="panel-block">
                      <div class="control is-expanded">
                        <form @submit.prevent="getTargeting">
                          <div class="field has-addons is-expanded">
                            <div class="control is-expanded">
                              <input
                                class="input is-fullwidth"
                                type="text"
                                v-model="sellerId"
                                placeholder="Seller id"
                              />
                            </div>
                            <div class="control is-expanded">
                              <input
                                class="input is-fullwidth"
                                type="text"
                                v-model="dealIdForTargeting"
                                placeholder="Deal ID / Private Auction Id"
                              />
                            </div>
                            <div class="control">
                              <a
                                class="button is-primary query-button"
                                v-on:click="getTargeting"
                                >Get Targeting</a
                              >
                            </div>
                          </div>
                        </form>
                      </div>
                    </div>
                    <nav class="panel">
											<p class="panel-heading is-fullwidth">Data Store Events</p>
                      <div class="field" style="height: 100%; width: 100%">
                        <div class="control" style="height: 100%; width: 100%">
                          <editor
                            class="box"
                            v-model="datastoreEventsJson"
                            @init="editorInit"
                            lang="json"
                            theme="chrome"
                            height="1046"
                          ></editor>
                        </div>
                      </div>
                    </nav>
                    <nav class="level">
                      <div class="level-item has-text-centered">
                        <nav class="panel" style="width: 100%">
                          <p class="panel-heading is-fullwidth">Data Store (Transformed)</p>
                          <div class="panel-block is-fullwidth">
                            <div
                              class="field"
                              style="height: 100%; width: 100%"
                            >
                              <div
                                class="control"
                                style="height: 100%; width: 100%"
                              >
                                <editor
                                  class="box"
                                  v-model="datastoreJson"
                                  @init="editorInit"
                                  lang="json"
                                  theme="chrome"
                                  height="1046"
                                ></editor>
                              </div>
                            </div>
                          </div>
                        </nav>
                      </div>
                      <div class="level-item has-text-centered">
                        <nav class="panel" style="width: 100%">
                          <p class="panel-heading is-fullwidth">Couchbase (Deal Targeting)</p>
                          <div class="panel-block is-fullwidth">
                            <div
                              class="field"
                              style="height: 100%; width: 100%"
                            >
                              <div
                                class="control"
                                style="height: 100%; width: 100%"
                              >
                                <editor
                                  class="box"
                                  v-model="couchbaseDTJson"
                                  @init="editorInit"
                                  lang="json"
                                  theme="chrome"
                                  height="1046"
                                ></editor>
                              </div>
                            </div>
                          </div>
                        </nav>
                      </div>
											<div class="level-item has-text-centered">
                        <nav class="panel" style="width: 100%">
                          <p class="panel-heading is-fullwidth">Couchbase (Private Auction)</p>
                          <div class="panel-block is-fullwidth">
                            <div
                              class="field"
                              style="height: 100%; width: 100%"
                            >
                              <div
                                class="control"
                                style="height: 100%; width: 100%"
                              >
                                <editor
                                  class="box"
                                  v-model="couchbasePAJson"
                                  @init="editorInit"
                                  lang="json"
                                  theme="chrome"
                                  height="1046"
                                ></editor>
                              </div>
                            </div>
                          </div>
                        </nav>
                      </div>
                    </nav>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import editor from "vue2-ace-editor";

export default {
  name: "Sync",
  components: {
    editor,
  },
  data() {
    return {
      syncResponse: "",
      datastoreJson: "",
			datastoreEventsJson: "",
      couchbaseDTJson: "",
      couchbasePAJson: "",
      sellerId: null,
      dealIdForTargeting: null,
      dealIdForCompare: null,
      compareResponse: ""
    };
  },
  created() {
    this.$http.options.root = window.location.host;
  },
  methods: {
    syncCouchBase() {
      this.$http.get("ds/deal-targeting-api/sync", { timeout: 5 * 60 * 1000 }).then(
        (response) => {
          this.syncResponse = response.bodyText;
        },
        () => {
          this.syncResponse = "Something went wrong syncing deal targeting";
        }
      );
    },
    compareSingleTargeting() {
      this.$http.get("ds/deal-targeting-api/compare", {
        params: {
            privateAuctionId: this.dealIdForCompare
        }}).then(
          (response) => {
            this.compareResponse = response.bodyText;
          },
          () => {
            this.syncResponse = "Something went wrong comparing deal targeting";
          }
      );
    },
    compareAllTargetings() {
      this.$http.get("ds/deal-targeting-api/compareAll").then(
          (response) => {
            this.syncResponse = response.bodyText;
          },
          () => {
            this.syncResponse = "Something went wrong comparing deal targeting";
          }
      );
    },
    getTargeting() {
      this.$http
        .get("ds/deal-targeting-api/check", {
          params: {
            sellerId: this.sellerId,
            targetingId: this.dealIdForTargeting,
          },
        })
        .then((response) => {
          return response.json();
        },
				(error) => {
					console.log(error);
					this.datastoreEventsJson = error.bodyText
          this.datastoreJson = ""
          this.couchbaseDTJson = ""
          this.couchbasePAJson = ""
				})
        .then((data) => {
					this.datastoreEventsJson = JSON.stringify(JSON.parse(data.eventsRedis), null, 4);
          this.datastoreJson = JSON.stringify(JSON.parse(data.patRedis), null, 4);
          this.couchbaseDTJson = JSON.stringify(JSON.parse(data.patCouchbase), null, 4);
          this.couchbasePAJson = data.patCouchbaseLegacy
				});
    },
    editorInit() {
      require("brace/ext/language_tools"); // language extension prerequsite...
      require("brace/mode/json"); // language
      require("brace/theme/chrome");
    },
  },
  beforeDestroy() {},
};
</script>
