<template>
    <div>
        <div class="box">
            <div class="columns">
                <div class="column tile is-parent">
                    <div class="tile is-child">
                        <p class="title is-4">Targeting store</p>
                        <nav class="panel">
                            <p class="panel-heading">
                                Query Targeting Store
                            </p>
                            <div class="panel-block">
                                <div class="control is-expanded">
                                    <form @submit.prevent="getTargeting">
                                        <div class="field has-addons is-expanded">
                                            <div class="control is-expanded">
                                                <input class="input is-fullwidth" type="text" v-model="sellerId" placeholder="Seller id">
                                            </div>
                                            <div class="control is-expanded">
                                                <input class="input is-fullwidth" type="text" v-model="dealIdForTargeting" placeholder="Deal / Bundle ID">
                                            </div>
                                            <div class="control">
                                                <a class="button is-primary query-button"
                                                   v-on:click="getTargeting">Get Targeting Rules</a>
                                            </div>
                                        </div>
                                        <div class="field has-addons is-expanded">
                                            <div class="control is-expanded">
                                                <input class="input is-fullwidth" type="text" v-model="dealIdRestrictions" placeholder="Deal ID">
                                            </div>
                                            <div class="control">
                                                <a class="button is-primary query-button"
                                                   v-on:click="getAdRestrictions">Ad Restrictions</a>
                                            </div>
                                        </div>
                                        <div class="field has-addons is-expanded">
                                            <div class="control is-expanded">
                                                <input class="input is-fullwidth" type="text" v-model="inventorySellerId" placeholder="Seller ID">
                                            </div>
                                            <div class="control">
                                                <a class="button is-primary query-button"
                                                   v-on:click="getInventory">Get Seller Inventory</a>
                                            </div>
                                        </div>
                                        <div class="field has-addons is-expanded">
                                            <div class="control is-expanded">
                                                <input class="input is-fullwidth" type="text" v-model="dealIdForSlots" placeholder="Deal ID">
                                            </div>
                                            <div class="control">
                                                <a class="button is-primary query-button"
                                                   v-on:click="getSlotsForDeal">Get Matching Slots For Deal</a>
                                            </div>
                                        </div>
                                        <div class="field has-addons is-expanded">
                                          <div class="control is-expanded">
                                            <input class="input is-fullwidth" type="text" v-model="dealIdForGap" placeholder="Deal ID">
                                          </div>
                                          <div class="control">
                                            <a class="button is-primary query-button"
                                               v-on:click="getDealDeliveryGap">Get Delivery Gap For Deal</a>
                                          </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </nav>
                        <nav class="panel">
                            <p class="panel-heading">
                                Tools
                            </p>
                            <div class="panel-block">
                                <div class="control">
                                    <a class="button is-primary"
                                       v-on:click="deleteFailures">Clear Failures History
                                    </a>
                                </div>
                            </div>
                        </nav>
                    </div>
                </div>
            </div>

        </div>
        <div class="box">
            <div class="tile is-ancestor">
                <div class="tile is-parent">
                    <div class="tile is-child">
                        <p class="title is-4">Editor</p>
                        <div class="field" style="height: 100%">
                            <div class="control" style="height: 100%">
                                <editor class="box" v-model="targetingJson" @init="editorInit" lang="json" theme="chrome" height="1046"></editor>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import editor from 'vue2-ace-editor'

export default {
        name: 'Store', components: {
            editor
        }, data() {
            return {
                sellerId: null, inventorySellerId: null, dealIdForSlots: null, dealIdForGap: null, dealIdForTargeting: null, dealIdRestrictions: null, targetingJson: ""
            }
        }, methods: {
            getAdRestrictions() {
                this.$http.get('ds/store/ad-restrictions', {params: {dealId: this.dealIdRestrictions}})
                        .then(response => {
                            return response.json();
                        }).then(data => {
                    this.targetingJson = JSON.stringify(data, null, 4);
                    console.log(this.targetingJson)
                });
            },
            getInventory() {
              this.$http.get('ds/store/inventory', {params: {sellerId: this.inventorySellerId}})
                  .then(response => {
                    return response.json();
                  }).then(data => {
                this.targetingJson = JSON.stringify(data, null, 4);
                console.log(this.targetingJson)
              });

            },
            getSlotsForDeal() {
              this.$http.get('ds/store/slots-for-deal', {params: {dealId: this.dealIdForSlots}})
                  .then(response => {
                    return response.json();
                  }).then(data => {
                this.targetingJson = JSON.stringify(data, null, 4);
                console.log(this.targetingJson)
              });

            },
            getDealDeliveryGap() {
              this.$http.get('ds/store/delivery-gap-for-deal', {params: {dealId: this.dealIdForGap}})
                  .then(response => {
                    return response.json();
                  }).then(data => {
                this.targetingJson = "Deal Delivery Gap: " + data + " seconds";
                console.log(this.targetingJson)
              });

            },
            getTargeting() {
                this.$http.get('ds/store/targeting', {params: {sellerId: this.sellerId, targetingId: this.dealIdForTargeting}})
                        .then(response => {
                            return response.json();
                        }).then(data => {
                    this.targetingJson = JSON.stringify(data, null, 4);
                    console.log(this.targetingJson)
                });
            },
            deleteFailures() {
                this.$http.delete('ds/store/clear-history')
                    .then(response => {
                        console.log("deleted all failures: " + response);
                    });
            },
            editorInit() {
                require('brace/ext/language_tools') // language extension prerequsite...
                require('brace/mode/json') // language
                require('brace/theme/chrome')
            },
        }

    }
</script>

<style>
.query-button {
 width: 225px;
}
</style>
