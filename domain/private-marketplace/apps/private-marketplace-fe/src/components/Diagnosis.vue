<template>
  <div>
    <div class="box">
      <div class="tile is-ancestor">
        <div class="tile is-parent">
          <div class="tile is-child">
            <p class="title is-4">SSP Pmp Entities</p>
            <article class="message is-info">
              <div class="message-body">
                The private marketplace apps in SSP (<strong>DS, DEAL-TARGETING-SERVICE</strong>) generate various entities for each deal and store them in different data stores for access by the delivery apps. This section verifies the presence of all necessary entities (all boxes must be green) to activate traffic delivery for the deal.
              </div>
            </article>
            <div class="deal-input-div">
              <input
                  class="input deal-input"
                  type="text"
                  v-model="selectedDeal"
                  placeholder="Deal ID / Private Auction Id"
              />
              <div class="control">
                <a class="button is-primary" v-on:click="fetchEntityList">
                  Get Diagnosis
                </a>
              </div>
            </div>
            <div>
              <div class="card-container-1">
                <div class="card" v-for="check in checks">
                    <header class="card-header">
                      <p class="card-header-title" :class="getStatus(check.status)">
                        {{ check.title }} - {{check.statusMessage}}
                      </p>
                    </header>
                    <div class="card-content">
                      <div v-if="check.subtitle !== null" class="media">
                        <div class="media-content">
                          <p class="subtitle has-text-weight-semibold">{{check.subtitle}}</p>
                        </div>
                      </div>
                      <div class="content">
                        <div v-for="line in check.summary">
                          <div>{{line}}</div>
                        </div>
                      </div>
                    </div>
                    <footer class="card-footer">
                      <a v-for="detail in check.details" class="card-footer-item" v-on:click="preview(detail)">{{ detail.buttonName }}</a>
                    </footer>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal" :class="isPreview">
          <div class="modal-background" v-on:click="closePreview"></div>
          <div class="modal-card">
            <header class="modal-card-head">
              <p class="modal-card-title">{{ previewJsonName }}</p>
              <button class="delete" aria-label="close" v-on:click="closePreview"></button>
            </header>
            <section class="modal-card-body">
              <editor class="box" v-model="previewJson" @init="editorInit" lang="json" theme="chrome" height="1046"></editor>
            </section>
            <footer class="modal-card-foot">
              <button class="button" v-on:click="closePreview">Cancel</button>
            </footer>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import editor from 'vue2-ace-editor';

// user mock response for development
import * as mockEntities from "./mock-entities-response.json";

export default {
  name: 'Diagnosis',
  components: {
    editor
  },

  data() {
    return {
      timer: '', checks: [], selectedDeal: null, previewJsonName: "", previewJson: "", isPreview: "", cols: 2
    }
  },

  created() {
    this.$http.options.root = window.location.host;
    //this.fetchAllData();
  },
  computed: {
    rows () {
      let rows = []
      let mid = Math.ceil(this.checks.length / this.cols)
      for (let col = 0; col < this.cols; col++) {
        rows.push(this.checks.slice(col * mid, col * mid + mid))
      }
      return rows
    }
  },

  methods: {
    fetchAllData: function () {
      this.fetchEntityList();
    },
    fetchEntityList: function () {
      this.$http.get('deal-monitor/diagnosis/byId', {
          params: { id: this.selectedDeal}
        }
      )
      .then(response => {
          return response.json()
      }).then(data => {
          this.checks = data.cards;
      });
      //using mock data
      //this.checks = mockEntities.checks;
      console.log(this.checks)
    },
    preview: function (value) {
      const decoded = atob(value.jsonString);
      const obj = JSON.parse(decoded);
      const formatted = JSON.stringify(obj, null, '\t');

      this.previewJson = formatted;
      this.previewJsonName = value.buttonName;
      this.isPreview = "is-active";
    },
    closePreview: function () {
      this.previewJsonName = "";
      this.previewJson = "";
      this.isPreview = ""
    },
    editorInit() {
      require('brace/ext/language_tools') // language extension prerequsite...
      require('brace/mode/json') // language
      require('brace/theme/chrome')
    },
    getStatus(status) {
      if (status === "OK") {
        return "has-background-success";
      }
      else if (status === "WARN") {
        return "has-background-warning";
      }
      else if (status === "ERROR") {
        return "has-background-danger";
      }
      else {
        return "";
      }
    }
  }
}
</script>

<style>
.card-container-1 {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-evenly;
}
.card-col-1 {
  flex-grow: 1;
  display: flex;
  flex-direction: row;
}
.card {
  width: 48%;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  justify-content: space-between;
  margin-bottom: 25px;
}

.card-content {
  flex-grow: 10;

}

.deal-input {
  max-width: 450px;
}
.deal-input-div {
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin-bottom: 15px;
}
</style>
