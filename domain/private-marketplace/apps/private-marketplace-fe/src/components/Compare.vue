<template>
  <div>
    <div class="box">
      <div class="tile is-ancestor">
        <div class="tile is-parent">
          <div class="tile is-child">
            <p class="title is-4">Tools</p>
            <nav class="panel">
              <div class="panel-block">
                <div class="field has-addons is-expanded">
                  <div class="control is-expanded">
                    <input
                        class="input is-fullwidth"
                        type="text"
                        v-model="dealIdForCompare"
                        placeholder="Deal ID / Private Auction Id"
                    />
                  </div>
                  <div class="control">
                    <a class="button is-primary" v-on:click="compareSingleTargeting">
                      Compare Single Targeting
                    </a>
                  </div>
                  <div class="control">
                       {{ compareResponse }}
                  </div>
                </div>
              </div>
              <div class="panel-block">
                <div class="control">
                  <a class="button is-primary" v-on:click="compareAllTargetings"
                  >Compare All Targetings
                  </a>
                </div>
              </div>
            </nav>
            <div class="box">
              <div class="tile is-ancestor">
                <div class="tile is-parent">
                  <div class="tile is-child">
                    <p class="title is-4">Compare</p>
                    <nav class="panel">
                      <p class="panel-heading is-fullwidth">Data Store Events</p>
                      <div class="field" style="height: 100%; width: 100%">
                        <div class="control" style="height: 100%; width: 100%">
                          <editor
                              class="box"
                              v-model="redisEvents"
                              @init="editorInit"
                              lang="json"
                              theme="chrome"
                              height="1046"
                          ></editor>
                        </div>
                      </div>
                    </nav>
                    <nav class="level">
                      <div class="level-item has-text-centered">
                        <nav class="panel" style="width: 100%">
                          <p class="panel-heading is-fullwidth">Deal Targeting</p>
                          <div class="panel-block is-fullwidth">
                            <div
                                class="field"
                                style="height: 100%; width: 100%"
                            >
                              <div
                                  class="control"
                                  style="height: 100%; width: 100%"
                              >
                                <editor
                                    class="box"
                                    v-model="newTargeting"
                                    @init="editorInit"
                                    lang="json"
                                    theme="chrome"
                                    height="1046"
                                ></editor>
                              </div>
                            </div>
                          </div>
                        </nav>
                      </div>
                      <div class="level-item has-text-centered">
                        <nav class="panel" style="width: 100%">
                          <p class="panel-heading is-fullwidth">Legacy Targeting</p>
                          <div class="panel-block is-fullwidth">
                            <div
                                class="field"
                                style="height: 100%; width: 100%"
                            >
                              <div
                                  class="control"
                                  style="height: 100%; width: 100%"
                              >
                                <editor
                                    class="box"
                                    v-model="legacyTargeting"
                                    @init="editorInit"
                                    lang="json"
                                    theme="chrome"
                                    height="1046"
                                ></editor>
                              </div>
                            </div>
                          </div>
                        </nav>
                      </div>
                    </nav>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import editor from "vue2-ace-editor";

export default {
  name: "Sync",
  components: {
    editor,
  },
  data() {
    return {
      legacyTargeting: "",
      newTargeting: "",
      dealIdForCompare: null,
      compareResponse: "",
      redisEvents: ""
    };
  },
  created() {
    this.$http.options.root = window.location.host;
  },
  methods: {
    compareSingleTargeting() {
      this.$http.get("ds/deal-targeting-api/compare", {
        params: {
          privateAuctionId: this.dealIdForCompare
        }}).then((response) => {
            return response.json();
          },
          (error) => {
            console.log(error);
            this.datastoreEventsJson = error.bodyText
            this.compareResponse = "error in comparison"
            this.legacyTargeting = "N/A"
            this.newTargeting = "N/A"
            this.redisEvents = "N/A"
          })
          .then((data) => {
            var tempLegacy = JSON.parse(data.legacyTargeting)
            var tempNew = JSON.parse(data.newTargeting)
            console.log(tempLegacy[0].inclusions.formats.length)
            if (tempLegacy[0].inclusions.formats !== null && tempLegacy[0].inclusions.formats.length === 67) {
              tempLegacy[0].inclusions.formats = null
              tempNew[0].inclusions.formats = null
            }
            tempNew[0].inclusions.slots = null;
            this.legacyTargeting = JSON.stringify(tempLegacy, null, 4);
            this.newTargeting = JSON.stringify(tempNew, null, 4);
            this.redisEvents = JSON.stringify(JSON.parse(data.redisEvents), null, 4);
            this.compareResponse = data.result;
          });
    },
    compareAllTargetings() {
      this.$http.get("ds/deal-targeting-api/compareAll").then(
          (response) => {
            this.syncResponse = response.bodyText;
          },
          () => {
            this.syncResponse = "Something went wrong comparing deal targeting";
          }
      );
    },
    editorInit() {
      require("brace/ext/language_tools"); // language extension prerequsite...
      require("brace/mode/json"); // language
      require("brace/theme/chrome");
    },
  },
  beforeDestroy() {},
};
</script>
