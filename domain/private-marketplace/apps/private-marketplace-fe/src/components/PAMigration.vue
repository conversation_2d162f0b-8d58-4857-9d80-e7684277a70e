<template>
  <div class="box">
    <div class="deal-input-div">
      <input
          class="input deal-input"
          type="text"
          v-model="dealIdForCompare"
          placeholder="Deal ID / Private Auction Id"
      />
      <div class="control">
        <a class="button is-primary" v-on:click="compareSinglePrivateAuction">
          Compare
        </a>
      </div>
    </div>
    <nav class="level">

      <div class="level-item has-text-centered">
        <nav class="panel" style="width: 100%">
          <p class="panel-heading is-fullwidth">GAS Private Auction</p>
          <div class="panel-block is-fullwidth">
            <div
                class="field"
                style="height: 100%; width: 100%"
            >
              <div
                  class="control"
                  style="height: 100%; width: 100%"
              >
                <editor
                    class="box"
                    v-model="legacyPrivateAuction"
                    @init="editorInit"
                    lang="json"
                    theme="chrome"
                    height="600"
                ></editor>
              </div>
            </div>
          </div>
        </nav>
      </div>

      <div class="level-item has-text-centered">
        <nav class="panel" style="width: 100%">
          <p class="panel-heading is-fullwidth">Deal Service Private Auction</p>
          <div class="panel-block is-fullwidth">
            <div
                class="field"
                style="height: 100%; width: 100%"
            >
              <div
                  class="control"
                  style="height: 100%; width: 100%"
              >
                <editor
                    class="box"
                    v-model="newPrivateAuction"
                    @init="editorInit"
                    lang="json"
                    theme="chrome"
                    height="600"
                ></editor>
              </div>
            </div>
          </div>

        </nav>
      </div>
    </nav>

    <div class="level-item has-text-centered">
      <nav class="panel" style="width: 100%">
        <p class="panel-heading is-fullwidth">Single Comparison Results</p>
        <div class="panel-block is-fullwidth">
          <div
              class="field"
              style="height: 100%; width: 100%"
          >
            <div
                class="control"
                style="height: 100%; width: 100%"
            >
              <editor
                  class="box"
                  v-model="compareDiff"
                  @init="editorInit"
                  lang="json"
                  theme="chrome"
                  height="600"
              ></editor>
            </div>
          </div>
        </div>
      </nav>
    </div>

    <div class="level-item has-text-centered">
      <nav class="panel" style="width: 100%">
        <p class="panel-heading is-fullwidth">All Comparison Results</p>
        <div class="panel-block is-fullwidth">
          <div
              class="field"
              style="height: 100%; width: 100%"
          >
            <div
                class="control"
                style="height: 100%; width: 100%"
            >
              <editor
                  class="box"
                  v-model="compareAllDiff"
                  @init="editorInit"
                  lang="json"
                  theme="chrome"
                  height="600"
              ></editor>
            </div>
          </div>
        </div>
      </nav>
    </div>

    <div class="box">
      <div class="deal-input-div">
        <div class="control">
          <a class="button is-primary" v-on:click="compareAllPrivateAuctions">
            Compare All Private Auctions
          </a>
        </div>
      </div>
    </div>

    <div class="box">
      <div class="deal-input-div">
        <div class="control">
          <a class="button is-primary" v-on:click="refreshAllPrivateAuctions">
            Refresh All Private Auctions
          </a>
        </div>
      </div>
    </div>

  </div>
</template>

<script>
import editor from "vue2-ace-editor";
import { diff } from 'json-diff-ts';

export default {
  name: "Migration",
  components: {
    editor,
  },
  data() {
    return {
      legacyPrivateAuction: "",
      newPrivateAuction: "",
      dealIdForCompare: null,
      compareDiff: "",
      compareAllDiff: "",
    };
  },
  created() {
    this.$http.options.root = window.location.host;
  },
  methods: {
    compareSinglePrivateAuction() {
      this.$http.get("deal-monitor/migration/singlePrivateAuction", {
        params: {
          id: this.dealIdForCompare
        }}).then((response) => {
            return response.json();
          },
          (error) => {
            console.log(error);
            this.datastoreEventsJson = error.bodyText
            this.compareResponse = "error in comparison"
            this.legacyTargeting = "N/A"
            this.newTargeting = "N/A"
            this.redisEvents = "N/A"
          })
          .then((data) => {

            if (data.legacyPA === "{}") {
              this.legacyPrivateAuction = data.legacyPA
            } else {
              var tempLegacy = JSON.parse(atob(data.legacyPA));
              this.legacyPrivateAuction = JSON.stringify(tempLegacy, null, 4);
            }

            if (data.newPA === "{}") {
              this.newPrivateAuction = data.newPA
            } else {
              var tempNew = JSON.parse(atob(data.newPA));
              this.newPrivateAuction = JSON.stringify(tempNew, null, 4);
            }

            if (this.legacyPrivateAuction === "{}" || this.newPrivateAuction === "{}") {
              this.compareDiff = "N/A"
            } else {
              this.compareDiff = this.doDiff(this.legacyPrivateAuction, this.newPrivateAuction)
            }
          });
    },
    compareAllPrivateAuctions() {
      this.$http.get("deal-monitor/migration/compareAllPrivateAuctions").then(
          (response) => {
            return response.json()
          },
          (error) => {
            console.log(error)
            this.compareAllDiff = "Something went wrong comparing PAs";
          }).then((data) => {
            this.compareAllDiff = JSON.stringify(data, null, 4);
          })
    },
    refreshAllPrivateAuctions() {
      this.$http.get("ds/control/triggerRefreshOfAllPrivateAuctions", {})
        .then((response) => {
          return response.json();
        })
    },
    doDiff(legacyPA, newPA) {
      return JSON.stringify({"diffs": diff(newPA, legacyPA)}, null, 4);
    },
    editorInit() {
      require("brace/ext/language_tools"); // language extension prerequsite...
      require("brace/mode/json"); // language
      require("brace/theme/chrome");
    },
  },
  beforeDestroy() {},
};
</script>

<style scoped>

</style>