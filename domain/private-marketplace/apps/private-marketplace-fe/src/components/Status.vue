<template>
    <div>
        <div class="box">
            <div class="tile is-ancestor">
                    <div class="tile is-parent">
                        <div class="tile is-child">
                            <p class="title is-4">Deal with failures</p>
                            <div>
                                <ul>
                                    <li v-for="fail in failures">
                                        <div>
                                            <div class="columns">
                                                <div class="column">
                                                    <div><span class="has-text-weight-bold">Deal ID: </span> {{fail.dealId}}</div>
                                                </div>
                                                <div class="column" >
                                                    <div>
                                                        <a class="button is-danger is-fullwidth" v-on:click="showFailures(fail.dealId)">{{fail.failures.length}} errors</a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div v-if="showErrors(fail.dealId)">
                                            <br />
                                            <div class="box">
                                                <table class="table">
                                                    <thead>
                                                    <tr>
                                                        <th>Event Id</th>
                                                        <th>Event Type</th>
                                                        <th>Reason</th>
                                                        <th>Time of Failure</th>
                                                        <th></th>
                                                        <th></th>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <tr v-for="failure in fail.failures">
                                                        <td style="vertical-align: middle;">{{failure.eventId}}</td>
                                                        <td style="vertical-align: middle;">{{failure.eventType}}</td>
                                                        <td style="vertical-align: middle;">{{failure.reason}}</td>
                                                        <td style="vertical-align: middle;">{{new Date(Date.parse(failure.timestamp)).toLocaleString()}}</td>
                                                        <td><a class="button is-light is-fullwidth is-primary" :href=failure.logUrl target="_blank">View in Logs</a></td>
                                                        <td><a class="button is-light is-fullwidth is-danger" v-on:click="skipEvent(failure.eventId)" target="_blank">Skip event</a></td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: 'Status', data() {
            return {
                timer: '',
                failures: [],
                reimportEventId: '',
                reimportDealId: '',
                processingImport: false,
                selectedDeal: null
            }
        }, created() {
            this.$http.options.root = window.location.host;
            this.fetchAllData();
            this.timer = setInterval(this.fetchAllData, 10000); // grab new data every 10 seconds
        }, methods: {
            fetchAllData: function () {
                this.fetchFailedEventsList();
            },
            fetchFailedEventsList: function () {
                this.$http.get('ds/history/failures')
                        .then(response => {
                            return response.json()
                        }).then(data => {
                    this.failures = data.failures;
                });
            }, reimportEvents: function () {
                this.processingImport = true;
                this.$http.get('ds/history/import', {params: {newerThanEventId: this.reimportEventId}}).then(response => {
                    this.processingImport = false;
                });
            }, reimportDeal: function () {
                this.processingImport = true;

                this.$http.get('ds/history/import-deal', {params: {dealId: this.reimportDealId}}).then(response => {
                    this.processingImport = false;
                });
            }, showFailures: function (dealId) {
                this.selectedDeal = this.selectedDeal === dealId ? null : dealId;
            }, showErrors: function (dealId) {
                return this.selectedDeal === dealId;
            },
            skipEvent: function (eventId) {
              this.$http.get(`ds/history/skip-event/${eventId}`).then(response => {
                this.fetchFailedEventsList();
              });
            }
        }, beforeDestroy() {
            clearInterval(this.timer)
        }
    }
</script>
