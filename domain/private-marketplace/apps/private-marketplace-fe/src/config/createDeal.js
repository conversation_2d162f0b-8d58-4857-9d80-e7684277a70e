export default {
    getCreationEvents() {
        return [{
            "type": "DealInitiated",
            "data": {
                "dealId": "new-deal",
                "sellerId": "ecab8507-16d1-4fbf-8cb9-137695ace74b"
            },
            "metadata": {
                "created": "2019-06-04T02:31:11.912Z",
                "eventId": "059b4bca3c712297e4ace77594bca868",
                "producer": {
                    "id": "new-deal",
                    "type": "Deal"
                }
            }
        }, {
            "type": "BuyerIdentified",
            "data": {
                "buyer": {
                    "id": "974522"
                },
                "dealId": "new-deal"
            },
            "metadata": {
                "created": "2019-06-04T02:31:21.270Z",
                "eventId": "e051ac626c57cf24274272f73648bd75",
                "producer": {
                    "id": "new-deal",
                    "type": "Deal"
                }
            }
        }, {
            "type": "DealNamed",
            "data": {
                "name": "PMP Mocks Test Deal",
                "dealId": "new-deal"
            },
            "metadata": {
                "created": "2019-06-04T02:31:27.458Z",
                "eventId": "2d692fbc877b22c1e796c814200194ec",
                "producer": {
                    "id": "new-deal",
                    "type": "Deal"
                }
            }
        }, {
            "type": "DealPriced",
            "data": {
                "price": {
                    "amount": 2,
                    "currency": "EUR"
                },
                "dealId": "new-deal"
            },
            "metadata": {
                "created": "2019-06-04T03:28:40.361Z",
                "eventId": "7530e878ccf947c0b30c51226729d2a6",
                "producer": {
                    "id": "new-deal",
                    "type": "Deal"
                }
            }
        }, {
            "type": "PriceTypeSelected",
            "data": {
                "dealId": "new-deal",
                "priceType": "fixed"
            },
            "metadata": {
                "created": "2019-06-04T03:28:45.774Z",
                "eventId": "a158677beadb3e5f6d031fd5fc6abde4",
                "producer": {
                    "id": "new-deal",
                    "type": "Deal"
                }
            }
        }, {
            "type": "PreferenceSelected",
            "data": {
                "preference": "preferred",
                "dealId": "new-deal"
            },
            "metadata": {
                "created": "2019-06-04T03:28:47.349Z",
                "eventId": "23dc2cb6175f97ec8ca1ddce609bed55",
                "producer": {
                    "id": "new-deal",
                    "type": "Deal"
                }
            }
        }, {
            "type": "TransparencyLevelSelected",
            "data": {
                "level": "transparent",
                "dealId": "new-deal"
            },
            "metadata": {
                "created": "2019-06-04T03:28:49.598Z",
                "eventId": "876dc67d31c3054e4850afc7e3a154ac",
                "producer": {
                    "id": "new-deal",
                    "type": "Deal"
                }
            }
        }, {
            "type": "FloorPricePolicySelected",
            "data": {
                "policy": "override",
                "dealId": "new-deal"
            },
            "metadata": {
                "created": "2019-06-04T03:29:43.192Z",
                "eventId": "49e7389775b5e8b8638ff83a6085f411",
                "producer": {
                    "id": "new-deal",
                    "type": "Deal"
                }
            }
        }, {
            "data": {
                "allowedAds": [{
                    "includes": [{
                        "ad": "any"
                    }]
                }],
                "dealId": "new-deal"
            },
            "metadata": {
                "created": "2019-06-04T03:29:44.993Z",
                "eventId": "2899a0d6e46655fd93c79b21ddde2993",
                "producer": {
                    "id": "new-deal",
                    "type": "Deal"
                }
            },
            "type": "AllowedAdsDefined"
        }, {
            "type": "BlacklistingPolicySelected",
            "data": {
                "policy": "respect",
                "dealId": "new-deal"
            },
            "metadata": {
                "created": "2019-06-04T03:29:53.150Z",
                "eventId": "9d6fee9368411d2ef86fd82b9355eadd",
                "producer": {
                    "id": "new-deal",
                    "type": "Deal"
                }
            }
        }, {
            "type": "DealActivated",
            "data": {
                "dealId": "new-deal"
            },
            "metadata": {
                "created": "2019-06-04T03:29:56.030Z",
                "eventId": "70e97c1897075f2739fec48000ac6f20",
                "producer": {
                    "id": "new-deal",
                    "type": "Deal"
                }
            }
        }, {
            "data": {
                "dealId": "new-deal",
                "targeting": {
                    "targets": [{
                        "inventorySegments": [{
                            "includes": [{
                                "adSlot": "any"
                            }]
                        }],
                        "audienceSegments": [{
                            "includes": [{
                                "consumer": "any"
                            }]
                        }]
                    }]
                }
            },
            "metadata": {
                "created": "2019-06-04T03:30:46.896Z",
                "eventId": "9d5e6016b5621040c4988527bf4a5838",
                "producer": {
                    "id": "new-deal",
                    "type": "Deal"
                }
            },
            "type": "TargetingDefined"
        }];
    }
}