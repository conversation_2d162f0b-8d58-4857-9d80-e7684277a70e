<template>
  <div id="app">
    <section class="hero is-info has-text-centered">
      <div class="hero-body">
        <div class="container">
          <h1 class="title">
            <span class="icon">
              <i class="fas fa-download"></i>
            </span>
            PMP Deal Importer
          </h1>
          <h2 class="subtitle">
            {{ subtitleMsg }}
          </h2>
        </div>
      </div>
    </section>
    <nav class="navbar is-link" role="navigation" aria-label="main navigation">
      <div id="navbarBasicExample" class="navbar-menu">
        <div class="navbar-start" style="flex-grow: 1; justify-content: center;">
          <a class="navbar-item"  v-if="isDev" v-on:click="displayItem = 'HOME'">
            Home
          </a>
          <a class="navbar-item" v-on:click="displayItem = 'STATUS'">
            Status
          </a>
          <a class="navbar-item" v-if="isDev" v-on:click="displayItem='STORE'">
            Store
          </a>
          <a class="navbar-item"  v-if="isDev" v-on:click="displayItem='SYNC'">
            Sync
          </a>
          <a class="navbar-item"  v-if="isDev" v-on:click="displayItem='COMPARE'">
            Compare
          </a>
          <a class="navbar-item" v-on:click="displayItem='DIAGNOSIS'">
            Diagnosis
          </a>
          <a class="navbar-item" v-on:click="displayItem='MIGRATION'">
            Migration
          </a>
        </div>
      </div>
    </nav>
    <section class="section">
      <div class="container">
        <Status v-if="displayItem === 'STATUS'"/>
        <Store v-if="displayItem === 'STORE'"/>
        <Sync v-if="displayItem === 'SYNC'"/>
        <Compare v-if="displayItem === 'COMPARE'"/>
        <Hub v-if="displayItem === 'HOME'"/>
        <Diagnosis v-if="displayItem === 'DIAGNOSIS'"/>
        <PAMigration v-if="displayItem === 'MIGRATION'"/>
      </div>
    </section>
  </div>
</template>

<script>
import Status from './components/Status'
import Store from './components/Store'
import Sync from './components/Sync'
import Compare from "@/components/Compare"
import Diagnosis from "@/components/Diagnosis"
import Hub from "@/components/Hub.vue";
import PAMigration from "@/components/PAMigration.vue";

export default {
  name: 'App',
  components: {
    PAMigration,
    Hub,
    Status,
    Store,
    Sync,
    Compare,
    Diagnosis
  },
  data() {
    return {
      displayItemdisplayMocks: false,
      displayItem: "STATUS",
      isDev: false
    }
  },
  created() {
    this.$http.options.root = window.location.host;
    const url = new URL(window.location.href);
    const queryParams = new URLSearchParams(url.search);
    this.isDev = queryParams.has("dev");
    console.log("is dev : " + this.isDev);

  },
  computed: {
    subtitleMsg: function() {
      switch (this.displayItem) {
        case "STATUS":
          return "service status and utilities";
        case "STORE":
          return "targeting store";
        case "SYNC":
          return "deal targeting sync";
        case "DIAGNOSIS":
          return "All information about a deal in the SSP";
      }
    }
  }
}
</script>

<style>

.section {
  padding: 2.5rem 2.5rem;
}

.container {
  max-width: 1600px;
}
</style>
